// Core application imports
import { CameraDebugService } from '../services/CameraDebugService.js';
import { InstructionView } from '../components/InstructionView.js';
import { ExamOrchestrator } from '../../application/services/ExamOrchestrator.js';
import { ICameraRepository } from '../../domain/repositories/ICameraRepository.js';

// Visualization services for facial landmark rendering
import { FaceVisualizationService } from '../services/FaceVisualizationService.js';
import { AnalysisVisualizationService } from '../services/AnalysisVisualizationService.js';
import { ExamAction } from '../services/ActionVisualizationService.js';

// Clinical analysis and integration services
import { ClinicalIntegrationService, ClinicalExaminationData, EnhancedClinicalResult } from '../services/ClinicalIntegrationService.js';
import { ClinicalComparisonIntegrationService, ComparisonExaminationData, EnhancedComparisonResult } from '../services/ClinicalComparisonIntegrationService.js';

// Movement detection and UI components
import { MovementDetectionService } from '../services/MovementDetectionService.js';
import { DetectionStatusView } from '../components/DetectionStatusView.js';
import { ExamActionType } from '../../shared/types/index.js';

// User interface and interaction services
import { SpeechService } from '../services/SpeechService.js';
import { ResultsView } from '../components/ResultsView.js';
import { ErrorView } from '../components/ErrorView.js';

// Advanced clinical detection services
import { SynkinesisDetectionService } from '../services/SynkinesisDetectionService.js';

/**
 * ExamController - Main controller for facial symmetry examination
 *
 * Orchestrates the entire examination process including:
 * - Camera initialization and landmark detection
 * - Real-time movement validation and peak frame tracking
 * - Clinical analysis integration and synkinesis detection
 * - Speech instructions and user interface management
 * - Results collection and export functionality
 */
export class ExamController {
  // === UI COMPONENTS ===
  /** Manages instruction display and speech synthesis */
  private instructionView: InstructionView;

  /** Prevents concurrent processing of camera frames */
  private isProcessing = false;

  /** Renders facial landmarks on live camera feed */
  private visualizationService: FaceVisualizationService | null = null;

  /** Renders detailed analysis visualization with measurements */
  private analysisVisualizationService: AnalysisVisualizationService | null = null;

  // === CLINICAL SERVICES ===
  /** Integrates with clinical analysis algorithms for Bell's palsy assessment */
  private clinicalIntegrationService: ClinicalIntegrationService;

  /** Provides comparative analysis between left and right facial movements */
  private clinicalComparisonService: ClinicalComparisonIntegrationService;

  /** Manages text-to-speech functionality for examination instructions */
  private speechService: SpeechService;

  /** Validates facial movements and tracks completion status */
  private movementDetectionService: MovementDetectionService;

  /** Displays real-time detection status and progress indicators */
  private detectionStatusView: DetectionStatusView;

  /** Advanced synkinesis detection using MediaPipe FaceMesh with clinical validation */
  private synkinesisDetectionService: SynkinesisDetectionService;

  // === EXAMINATION STATE ===
  /** Tracks whether the examination has been completed */
  private examCompleted = false;

  /** Stores final examination results for display and export */
  private examResults: any = null;

  /** Enhanced clinical analysis results with House-Brackmann grading */
  private clinicalResults: EnhancedClinicalResult | null = null;

  /** Comparative analysis results for left-right facial movement comparison */
  private comparisonResults: EnhancedComparisonResult | null = null;

  /** Handles results page rendering and export functionality */
  private resultsView: ResultsView;

  /** Track last action name to avoid repeated logs and speech */
  private _lastActionName: string = '';

  // === LANDMARK DATA STORAGE ===
  /** Real landmark storage for clinical analysis - stores peak frames for each action */
  private landmarkStorage: Map<string, any[]> = new Map();

  /** Current action being performed (for landmark storage mapping) */
  private currentActionName: string = '';

  /** Baseline landmarks from neutral face (legacy - kept for compatibility) */
  private baselineLandmarks: any[] | null = null;

  // === PATIENT DATA ===
  /** Store patient data for use throughout the exam and in results */
  private patientData: { id: string; name: string; age: string } | null = null;

  // === 🎯 PEAK FRAME TRACKING SYSTEM ===
  /**
   * Advanced peak frame tracking system that automatically captures
   * the frame with maximum facial movement for each action.
   *
   * This ensures consistent, high-quality landmark data for clinical analysis
   * by eliminating user timing dependency and capturing optimal expressions.
   */
  private peakFrameTracking = {
    /** Whether peak tracking is currently active */
    isTracking: false,

    /** Current facial action being tracked (smile, eyebrow_raise, etc.) */
    currentAction: '',

    /** Baseline landmarks for movement comparison */
    baselineLandmarks: null as any[] | null,

    /** Highest movement magnitude detected so far */
    peakMovement: 0,

    /** Landmarks from the frame with peak movement */
    peakFrame: null as any[] | null,

    /** Mouth movement measurements captured at peak frame (for smile actions) */
    peakMouthMovements: null as {
      leftVerticalDistance: number;
      rightVerticalDistance: number;
      leftHorizontalDistance: number;
      rightHorizontalDistance: number;
      verticalAsymmetry: number;
      horizontalAsymmetry: number;
    } | null,

    /** Timestamp when peak movement was detected */
    peakTimestamp: 0,

    /** Total number of frames processed during tracking */
    frameCount: 0,

    /** History of recent movement values (last 30 frames) */
    movementHistory: [] as number[],

    /** When tracking started (for duration calculation) */
    actionStartTime: 0
  };

  /**
   * Initialize the ExamController with all required services and dependencies
   *
   * @param examOrchestrator - Manages examination flow and action sequences
   * @param cameraRepository - Handles camera operations and MediaPipe integration
   */
  constructor(
    private examOrchestrator: ExamOrchestrator,
    private cameraRepository: ICameraRepository
  ) {
    // Initialize UI components
    this.instructionView = new InstructionView();

    // Initialize clinical analysis services
    this.clinicalIntegrationService = new ClinicalIntegrationService();
    this.clinicalComparisonService = new ClinicalComparisonIntegrationService();

    // Initialize user interaction services
    this.speechService = new SpeechService();
    this.movementDetectionService = new MovementDetectionService();
    this.detectionStatusView = new DetectionStatusView();

    // Initialize advanced detection services
    this.synkinesisDetectionService = new SynkinesisDetectionService();

    // Initialize results handling
    this.resultsView = new ResultsView(this.clinicalComparisonService);

    // Set up event listeners for UI interactions
    this.setupEventListeners();

    // Test House-Brackmann calculation with sample data for validation
    console.log('Testing House-Brackmann calculation:');
    console.log('Normal subject (2%, 3%, 4%):', this.calculateRealTimeHouseBrackmannGrade(2, 3, 4));
    console.log('Mild dysfunction (8%, 10%, 12%):', this.calculateRealTimeHouseBrackmannGrade(8, 10, 12));
    console.log('Severe dysfunction (40%, 50%, 60%):', this.calculateRealTimeHouseBrackmannGrade(40, 50, 60));
  }

  // === 🎯 PEAK FRAME TRACKING METHODS ===

  /**
   * Calculate movement magnitude for different facial actions
   *
   * This is the core method that quantifies facial movement for peak tracking.
   * Each action type uses specific landmarks and calculation methods optimized
   * for clinical accuracy and peak detection reliability.
   *
   * @param actionType - Type of facial action (smile, eyebrow_raise, eye_close, etc.)
   * @param currentLandmarks - Current frame landmarks (468+ points)
   * @param baselineLandmarks - Baseline/neutral landmarks for comparison
   * @returns Movement magnitude (scaled for peak comparison)
   */
  private calculateActionMovement(actionType: string, currentLandmarks: any[], baselineLandmarks: any[]): number {
    // Validate input landmarks (require full MediaPipe FaceMesh)
    if (!currentLandmarks || !baselineLandmarks || currentLandmarks.length < 468 || baselineLandmarks.length < 468) {
      return 0;
    }

    try {
      // Route to action-specific calculation methods
      switch (actionType.toLowerCase()) {
        case 'smile':
          return this.calculateSmileMovement(currentLandmarks, baselineLandmarks);
        case 'eyebrow_raise':
        case 'eyebrow_elevation':
          return this.calculateEyebrowMovement(currentLandmarks, baselineLandmarks);
        case 'eye_close':
        case 'gentle_eye_closure':
          return this.calculateEyeClosureMovement(currentLandmarks, baselineLandmarks);
        case 'lip_pucker':
        case 'pucker_lips':
          return this.calculateLipPuckerMovement(currentLandmarks, baselineLandmarks);
        case 'cheek_puff':
        case 'blow_cheeks':
          return this.calculateCheekPuffMovement(currentLandmarks, baselineLandmarks);
        default:
          console.warn(`Unknown action type for movement calculation: ${actionType}`);
          return 0;
      }
    } catch (error) {
      console.error(`Error calculating movement for ${actionType}:`, error);
      return 0;
    }
  }

  /**
   * IMPROVED: Calculate smile movement using clinically accurate method
   * Based on the provided analyze_smile_movement function
   */
  private calculateSmileMovement(currentLandmarks: any[], baselineLandmarks: any[]): number {
    console.log(`🔬 IMPROVED SMILE MOVEMENT ANALYSIS - Using Clinical Method`);

    // Use the improved analyze_smile_movement function
    const result = this.analyze_smile_movement(baselineLandmarks, currentLandmarks);

    console.log(`📊 CLINICAL SMILE ANALYSIS RESULTS:`);
    console.log(`   Left Movement: ${result.left_movement.toFixed(6)}`);
    console.log(`   Right Movement: ${result.right_movement.toFixed(6)}`);
    console.log(`   Asymmetry Index: ${result.asymmetry_index.toFixed(6)}`);
    console.log(`   Severity: ${result.severity}`);
    console.log(`   Affected Side: ${result.affected_side}`);

    // Calculate total movement for peak tracking (scale appropriately)
    const totalMovement = result.left_movement + result.right_movement;
    const scaledMovement = totalMovement * 1000; // Scale for peak tracking comparison

    console.log(`🎯 PEAK TRACKING VALUES:`);
    console.log(`   Total Movement: ${totalMovement.toFixed(6)}`);
    console.log(`   Scaled for Tracking: ${scaledMovement.toFixed(2)}`);
    console.log(`   Expected range: 10-100 (scaled)`);
    console.log(`   Classification: ${scaledMovement < 10 ? '❌ TOO LOW' : scaledMovement > 100 ? '❌ TOO HIGH' : '✅ NORMAL RANGE'}`);

    return scaledMovement;
  }

  /**
   * POSE-INVARIANT 3D SMILE SYMMETRY ANALYSIS
   *
   * Analyzes mouth corner symmetry using 3D pose-invariant methods that compensate
   * for head rotation, tilt, and distance variations. Uses anatomical midline
   * projection and normalized movement measurements.
   *
   * @param restLandmarks - Baseline facial landmarks (rest position)
   * @param smileLandmarks - Peak smile facial landmarks
   * @returns An object containing:
   *   - `left_movement`: Normalized left side movement relative to face width
   *   - `right_movement`: Normalized right side movement relative to face width
   *   - `asymmetry_index`: Pose-invariant asymmetry measurement
   *   - `severity`: Clinical classification based on movement asymmetry
   *   - `affected_side`: Side with reduced movement (Bell's palsy indicator)
   *
   * @remarks
   * - Uses 3D landmark coordinates (x, y, z) for pose compensation
   * - Projects corners to anatomical midline for true symmetry measurement
   * - Normalizes by face width for scale invariance
   * - Robust against head pose variations
   */
  private analyze_smile_movement(restLandmarks: any[], smileLandmarks: any[]) {
    console.log(`🔬 POSE-INVARIANT 3D SMILE SYMMETRY ANALYSIS`);

    // === HELPER FUNCTIONS FOR 3D POSE-INVARIANT ANALYSIS ===
    const distance3D = (a: any, b: any): number => {
      const dx = a.x - b.x;
      const dy = a.y - b.y;
      const dz = a.z - b.z;
      return Math.sqrt(dx * dx + dy * dy + dz * dz);
    };

    const averageMotion = (landmarksBefore: any[], landmarksAfter: any[], indices: number[]): number => {
      const motions = indices.map(index => distance3D(landmarksBefore[index], landmarksAfter[index]));
      return motions.reduce((a, b) => a + b, 0) / motions.length;
    };

    const normalizeVector = (v: any) => {
      const length = Math.sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
      return { x: v.x / length, y: v.y / length, z: v.z / length };
    };

    const getMidlineDirection = (nose: any, chin: any) => {
      return normalizeVector({
        x: chin.x - nose.x,
        y: chin.y - nose.y,
        z: chin.z - nose.z,
      });
    };

    const projectToMidline = (point: any, lineStart: any, direction: any) => {
      const px = point.x - lineStart.x;
      const py = point.y - lineStart.y;
      const pz = point.z - lineStart.z;
      const dot = px * direction.x + py * direction.y + pz * direction.z;
      return {
        x: lineStart.x + dot * direction.x,
        y: lineStart.y + dot * direction.y,
        z: lineStart.z + dot * direction.z,
      };
    };

    try {
      // === STEP 0: VALIDATE INPUT DATA ===
      console.log(`🔍 VALIDATING INPUT DATA:`);
      console.log(`   Rest landmarks count: ${restLandmarks?.length || 'undefined'}`);
      console.log(`   Smile landmarks count: ${smileLandmarks?.length || 'undefined'}`);
      console.log(`   Analysis type: 3D pose-invariant movement analysis`);

      if (!restLandmarks || !smileLandmarks) {
        console.error('❌ ERROR: Missing landmarks for pose-invariant analysis');
        console.error(`   Rest landmarks: ${restLandmarks ? 'OK' : 'MISSING'}`);
        console.error(`   Smile landmarks: ${smileLandmarks ? 'OK' : 'MISSING'}`);
        return {
          left_movement: 0,
          right_movement: 0,
          asymmetry_index: 1,
          severity: 'Analysis Error - Missing Landmarks',
          affected_side: 'Unknown',
          analysis_method: 'pose_invariant_error'
        };
      }

      if (restLandmarks.length < 468 || smileLandmarks.length < 468) {
        console.error(`❌ ERROR: Insufficient landmarks for analysis`);
        console.error(`   Rest: ${restLandmarks.length}, Smile: ${smileLandmarks.length} (need 468 each)`);
        return {
          left_movement: 0,
          right_movement: 0,
          asymmetry_index: 1,
          severity: 'Analysis Error - Insufficient Landmarks',
          affected_side: 'Unknown',
          analysis_method: 'pose_invariant_error'
        };
      }

      // === STEP 1: IMPLEMENT EXPERT'S POSE-INVARIANT ANALYSIS ===
      console.log(`🎯 IMPLEMENTING 3D POSE-INVARIANT SMILE ASYMMETRY ANALYSIS:`);

      // Define landmark indices for left and right mouth regions (expert's approach)
      const leftIndices = [48, 61, 78, 80];   // Left mouth region landmarks
      const rightIndices = [291, 308, 328, 310]; // Right mouth region landmarks

      console.log(`   Left mouth landmarks: [${leftIndices.join(', ')}]`);
      console.log(`   Right mouth landmarks: [${rightIndices.join(', ')}]`);

      // === STEP 2: CALCULATE 3D MOVEMENT FOR EACH SIDE ===
      console.log(`📏 CALCULATING 3D MOVEMENT DISTANCES:`);

      const leftMotion = averageMotion(restLandmarks, smileLandmarks, leftIndices);
      const rightMotion = averageMotion(restLandmarks, smileLandmarks, rightIndices);

      console.log(`   Left side 3D motion: ${leftMotion.toFixed(6)}`);
      console.log(`   Right side 3D motion: ${rightMotion.toFixed(6)}`);

      // === STEP 3: NORMALIZE BY FACE WIDTH (POSE-INVARIANT SCALING) ===
      console.log(`📐 NORMALIZING BY FACE WIDTH:`);

      const faceWidth = distance3D(restLandmarks[33], restLandmarks[263]); // IPD as face width
      const leftNorm = leftMotion / faceWidth;
      const rightNorm = rightMotion / faceWidth;

      console.log(`   Face width (IPD): ${faceWidth.toFixed(6)}`);
      console.log(`   Left normalized movement: ${leftNorm.toFixed(6)}`);
      console.log(`   Right normalized movement: ${rightNorm.toFixed(6)}`);

      // === STEP 4: CALCULATE POSE-INVARIANT ASYMMETRY ===
      console.log(`🔧 CALCULATING POSE-INVARIANT ASYMMETRY:`);

      const asymmetry = Math.abs(leftNorm - rightNorm) / Math.max(leftNorm, rightNorm);

      console.log(`   Movement difference: ${Math.abs(leftNorm - rightNorm).toFixed(6)}`);
      console.log(`   Max movement: ${Math.max(leftNorm, rightNorm).toFixed(6)}`);
      console.log(`   Asymmetry index: ${asymmetry.toFixed(6)} (${(asymmetry * 100).toFixed(1)}%)`);

      // === DEBUGGING: CHECK IF VALUES ARE REALISTIC ===
      console.log(`🚨 DEBUGGING EXPERT VALUES:`);
      console.log(`   Raw left motion: ${leftMotion.toFixed(6)}`);
      console.log(`   Raw right motion: ${rightMotion.toFixed(6)}`);
      console.log(`   Face width (IPD): ${faceWidth.toFixed(6)}`);
      console.log(`   Left normalized: ${leftNorm.toFixed(6)} (should be ~0.01-0.3)`);
      console.log(`   Right normalized: ${rightNorm.toFixed(6)} (should be ~0.01-0.3)`);
      console.log(`   Asymmetry: ${asymmetry.toFixed(6)} (should be ~0.05-0.2 for normal)`);

      if (leftNorm > 0.5 || rightNorm > 0.5) {
        console.warn(`⚠️ WARNING: Normalized values too high! This suggests an issue with the calculation.`);
      }
      if (asymmetry > 0.8) {
        console.warn(`⚠️ WARNING: Asymmetry too high for normal individual! Check calculation.`);
      }

      // === STEP 5: VALIDATE LANDMARK QUALITY ===
      console.log(`🔍 LANDMARK QUALITY VALIDATION:`);

      const criticalLandmarks = [33, 263, 61, 291, 48, 78, 80, 308, 328, 310];
      const validLandmarks = criticalLandmarks.filter(index => {
        const restLM = restLandmarks[index];
        const smileLM = smileLandmarks[index];
        return restLM && smileLM &&
               restLM.x >= 0.1 && restLM.x <= 0.9 &&
               smileLM.x >= 0.1 && smileLM.x <= 0.9;
      }).length;

      const landmarkCoverage = validLandmarks / criticalLandmarks.length;
      const coverageQuality = landmarkCoverage >= 0.8 ? 'Excellent' :
                             landmarkCoverage >= 0.6 ? 'Good' :
                             landmarkCoverage >= 0.4 ? 'Fair' : 'Poor';

      console.log(`   Valid critical landmarks: ${validLandmarks}/${criticalLandmarks.length}`);
      console.log(`   Landmark coverage: ${(landmarkCoverage * 100).toFixed(1)}% (${coverageQuality})`);

      // === STEP 6: DETERMINE CLINICAL SEVERITY AND AFFECTED SIDE ===
      console.log(`🏥 CLINICAL ASSESSMENT:`);

      // Determine affected side based on movement comparison
      let affectedSide = 'None';
      if (leftNorm < rightNorm * 0.8) {
        affectedSide = 'Left';
        console.log(`   ⚠️ Left side shows reduced movement (possible left-sided weakness)`);
      } else if (rightNorm < leftNorm * 0.8) {
        affectedSide = 'Right';
        console.log(`   ⚠️ Right side shows reduced movement (possible right-sided weakness)`);
      } else {
        console.log(`   ✅ Balanced movement on both sides`);
      }

      // Clinical severity classification
      let severity = 'Normal';
      if (asymmetry <= 0.1) {
        severity = 'Normal';
      } else if (asymmetry <= 0.25) {
        severity = 'Mild Asymmetry';
      } else if (asymmetry <= 0.5) {
        severity = 'Moderate Asymmetry';
      } else {
        severity = 'Severe Asymmetry';
      }

      console.log(`   Asymmetry classification: ${severity}`);
      console.log(`   Affected side: ${affectedSide}`);
      console.log(`   Movement ratio (L/R): ${(leftNorm / rightNorm).toFixed(3)}`);

      // === STEP 7: RETURN EXPERT'S POSE-INVARIANT RESULTS ===
      console.log(`📊 RETURNING POSE-INVARIANT ANALYSIS RESULTS:`);
      console.log(`   Left movement (normalized): ${leftNorm.toFixed(6)}`);
      console.log(`   Right movement (normalized): ${rightNorm.toFixed(6)}`);
      console.log(`   Asymmetry index: ${asymmetry.toFixed(6)} (${(asymmetry * 100).toFixed(1)}%)`);
      console.log(`   Severity: ${severity}`);
      console.log(`   Affected side: ${affectedSide}`);

      // === STEP 8: RETURN EXPERT'S POSE-INVARIANT RESULTS ===
      return {
        // Primary measurements using expert's 3D pose-invariant approach
        left_movement: leftNorm,
        right_movement: rightNorm,
        asymmetry_index: asymmetry,
        severity,
        affected_side: affectedSide,

        // Enhanced displacement data (3D pose-invariant method)
        horizontal_displacement: {
          left: leftNorm,
          right: rightNorm,
          asymmetry: Math.abs(leftNorm - rightNorm),
          method: '3d_pose_invariant_movement'
        },

        // 3D analysis metadata
        midline_analysis: {
          face_width_ipd: faceWidth,
          landmark_coverage: landmarkCoverage,
          coverage_quality: coverageQuality,
          analysis_type: '3d_pose_invariant'
        },

        // Movement measurements (normalized by face width)
        distance_measurements: {
          left_corner: {
            raw_3d_movement: leftMotion,
            normalized_movement: leftNorm
          },
          right_corner: {
            raw_3d_movement: rightMotion,
            normalized_movement: rightNorm
          }
        },

        // Quality metrics (3D landmark validation)
        measurement_quality: {
          landmark_coverage_percent: landmarkCoverage * 100,
          coverage_quality: coverageQuality,
          valid_landmarks: validLandmarks,
          total_landmarks: criticalLandmarks.length,
          overall_quality: coverageQuality
        },

        // Method metadata
        analysis_method: '3d_pose_invariant_movement_analysis',
        landmarks_used: {
          left_mouth_region: leftIndices,
          right_mouth_region: rightIndices,
          face_width_reference: [33, 263]
        },
        landmark_validation: {
          valid_landmarks: validLandmarks,
          total_required: criticalLandmarks.length
        }
      };

    } catch (error) {
      console.error('❌ CRITICAL ERROR in landmark-relative displacement analysis:', error);
      console.error('Error details:', {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : 'No stack trace',
        restLandmarksLength: restLandmarks?.length,
        smileLandmarksLength: smileLandmarks?.length
      });

      return {
        left_movement: 0,
        right_movement: 0,
        asymmetry_index: 1,
        severity: 'Analysis Error - Exception Thrown',
        affected_side: 'Unknown',
        analysis_method: 'landmark_relative_displacement_error',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Helper method: Get clinical interpretation of displacement measurements
   */
  private getDisplacementInterpretation(leftMovement: number, rightMovement: number, asymmetryIndex: number): string {
    const avgMovement = (Math.abs(leftMovement) + Math.abs(rightMovement)) / 2;

    if (avgMovement < 0.01) {
      return "Minimal facial movement detected - possible bilateral weakness";
    } else if (asymmetryIndex <= 0.05) {
      return "Excellent bilateral facial movement with normal symmetry";
    } else if (asymmetryIndex <= 0.15) {
      return "Good facial movement with mild asymmetry - monitor for progression";
    } else if (asymmetryIndex <= 0.30) {
      return "Moderate facial asymmetry - consider therapeutic intervention";
    } else {
      return "Severe facial asymmetry - immediate clinical evaluation recommended";
    }
  }

  /**
   * Helper method: Calculate consistency between different reference measurements
   */
  private calculateReferenceConsistency(
    leftNose: number, leftUpperLip: number, leftChin: number,
    rightNose: number, rightUpperLip: number, rightChin: number
  ): number {
    // Calculate coefficient of variation for each side
    const leftMeasurements = [Math.abs(leftNose), Math.abs(leftUpperLip), Math.abs(leftChin)];
    const rightMeasurements = [Math.abs(rightNose), Math.abs(rightUpperLip), Math.abs(rightChin)];

    const leftMean = leftMeasurements.reduce((sum, val) => sum + val, 0) / leftMeasurements.length;
    const rightMean = rightMeasurements.reduce((sum, val) => sum + val, 0) / rightMeasurements.length;

    if (leftMean === 0 || rightMean === 0) return 0;

    const leftVariance = leftMeasurements.reduce((sum, val) => sum + Math.pow(val - leftMean, 2), 0) / leftMeasurements.length;
    const rightVariance = rightMeasurements.reduce((sum, val) => sum + Math.pow(val - rightMean, 2), 0) / rightMeasurements.length;

    const leftCV = Math.sqrt(leftVariance) / leftMean;
    const rightCV = Math.sqrt(rightVariance) / rightMean;

    // Return consistency score (1 = perfect consistency, 0 = high variation)
    const avgCV = (leftCV + rightCV) / 2;
    return Math.max(0, 1 - avgCV);
  }

  /**
   * Helper method: Get quality grade from measurement quality metrics (vertical midline method)
   */
  private getQualityGrade(quality: any): string {
    const ipdStability = quality.ipd_stability || 1;
    const midlineQualityScore = quality.midline_quality_score || 0;
    const landmarkCoverage = quality.landmark_coverage_percent || 0;

    if (ipdStability < 0.05 && midlineQualityScore > 0.8 && landmarkCoverage > 80) {
      return 'Excellent';
    } else if (ipdStability < 0.10 && midlineQualityScore > 0.6 && landmarkCoverage > 60) {
      return 'Good';
    } else if (ipdStability < 0.20 && midlineQualityScore > 0.4 && landmarkCoverage > 40) {
      return 'Fair';
    } else {
      return 'Poor';
    }
  }

  /**
   * Helper method: Get overall quality assessment for vertical midline method
   */
  private getVerticalMidlineQuality(midlineQuality: string, coverageQuality: string, ipdStability: number): string {
    const qualityScores: { [key: string]: number } = {
      'Excellent': 4,
      'Good': 3,
      'Fair': 2,
      'Poor': 1
    };

    const midlineScore = qualityScores[midlineQuality] || 1;
    const coverageScore = qualityScores[coverageQuality] || 1;
    const ipdScore = ipdStability < 0.05 ? 4 : ipdStability < 0.10 ? 3 : ipdStability < 0.20 ? 2 : 1;

    const averageScore = (midlineScore + coverageScore + ipdScore) / 3;

    if (averageScore >= 3.5) return 'Excellent';
    if (averageScore >= 2.5) return 'Good';
    if (averageScore >= 1.5) return 'Fair';
    return 'Poor';
  }

  /**
   * Calculate eyebrow movement using key eyebrow landmarks
   */
  private calculateEyebrowMovement(currentLandmarks: any[], baselineLandmarks: any[]): number {
    // Key eyebrow landmarks: 70, 107 (left), 300, 336 (right)
    const leftEyebrowCurrent = currentLandmarks[70];
    const rightEyebrowCurrent = currentLandmarks[300];
    const leftEyebrowBaseline = baselineLandmarks[70];
    const rightEyebrowBaseline = baselineLandmarks[300];

    if (!leftEyebrowCurrent || !rightEyebrowCurrent || !leftEyebrowBaseline || !rightEyebrowBaseline) {
      return 0;
    }

    // Calculate vertical movement (eyebrow elevation)
    const leftMovement = Math.abs(leftEyebrowBaseline.y - leftEyebrowCurrent.y);
    const rightMovement = Math.abs(rightEyebrowBaseline.y - rightEyebrowCurrent.y);

    // Average movement
    const totalMovement = (leftMovement + rightMovement) / 2;

    console.log(`🔍 Eyebrow Movement: Left=${leftMovement.toFixed(4)}, Right=${rightMovement.toFixed(4)}, Total=${totalMovement.toFixed(4)}`);

    return totalMovement * 1000; // Scale for better comparison
  }

  /**
   * Calculate eye closure movement
   */
  private calculateEyeClosureMovement(currentLandmarks: any[], baselineLandmarks: any[]): number {
    // Eye landmarks: 159, 145 (left), 386, 374 (right)
    const leftEyeCurrent = {
      top: currentLandmarks[159],
      bottom: currentLandmarks[145]
    };
    const rightEyeCurrent = {
      top: currentLandmarks[386],
      bottom: currentLandmarks[374]
    };
    const leftEyeBaseline = {
      top: baselineLandmarks[159],
      bottom: baselineLandmarks[145]
    };
    const rightEyeBaseline = {
      top: baselineLandmarks[386],
      bottom: baselineLandmarks[374]
    };

    if (!leftEyeCurrent.top || !leftEyeCurrent.bottom || !rightEyeCurrent.top || !rightEyeCurrent.bottom ||
        !leftEyeBaseline.top || !leftEyeBaseline.bottom || !rightEyeBaseline.top || !rightEyeBaseline.bottom) {
      return 0;
    }

    // Calculate eye opening (distance between top and bottom)
    const leftCurrentOpening = Math.abs(leftEyeCurrent.top.y - leftEyeCurrent.bottom.y);
    const rightCurrentOpening = Math.abs(rightEyeCurrent.top.y - rightEyeCurrent.bottom.y);
    const leftBaselineOpening = Math.abs(leftEyeBaseline.top.y - leftEyeBaseline.bottom.y);
    const rightBaselineOpening = Math.abs(rightEyeBaseline.top.y - rightEyeBaseline.bottom.y);

    // Calculate closure as reduction in opening
    const leftClosure = Math.max(0, leftBaselineOpening - leftCurrentOpening);
    const rightClosure = Math.max(0, rightBaselineOpening - rightCurrentOpening);

    const totalClosure = (leftClosure + rightClosure) / 2;

    console.log(`🔍 Eye Closure Movement: Left=${leftClosure.toFixed(4)}, Right=${rightClosure.toFixed(4)}, Total=${totalClosure.toFixed(4)}`);

    return totalClosure * 1000; // Scale for better comparison
  }

  /**
   * Calculate lip pucker movement
   */
  private calculateLipPuckerMovement(currentLandmarks: any[], baselineLandmarks: any[]): number {
    // Lip landmarks for pucker: 13, 14 (top), 17, 18 (bottom)
    const topLipCurrent = { left: currentLandmarks[13], right: currentLandmarks[14] };
    const bottomLipCurrent = { left: currentLandmarks[17], right: currentLandmarks[18] };
    const topLipBaseline = { left: baselineLandmarks[13], right: baselineLandmarks[14] };
    const bottomLipBaseline = { left: baselineLandmarks[17], right: baselineLandmarks[18] };

    if (!topLipCurrent.left || !topLipCurrent.right || !bottomLipCurrent.left || !bottomLipCurrent.right ||
        !topLipBaseline.left || !topLipBaseline.right || !bottomLipBaseline.left || !bottomLipBaseline.right) {
      return 0;
    }

    // Calculate lip width reduction (pucker effect)
    const currentTopWidth = Math.abs(topLipCurrent.right.x - topLipCurrent.left.x);
    const currentBottomWidth = Math.abs(bottomLipCurrent.right.x - bottomLipCurrent.left.x);
    const baselineTopWidth = Math.abs(topLipBaseline.right.x - topLipBaseline.left.x);
    const baselineBottomWidth = Math.abs(bottomLipBaseline.right.x - bottomLipBaseline.left.x);

    const topReduction = Math.max(0, baselineTopWidth - currentTopWidth);
    const bottomReduction = Math.max(0, baselineBottomWidth - currentBottomWidth);

    const totalReduction = (topReduction + bottomReduction) / 2;

    console.log(`🔍 Lip Pucker Movement: Top=${topReduction.toFixed(4)}, Bottom=${bottomReduction.toFixed(4)}, Total=${totalReduction.toFixed(4)}`);

    return totalReduction * 1000; // Scale for better comparison
  }

  /**
   * Calculate cheek puff movement
   */
  private calculateCheekPuffMovement(currentLandmarks: any[], baselineLandmarks: any[]): number {
    // Cheek landmarks: 234 (left), 454 (right)
    const leftCheekCurrent = currentLandmarks[234];
    const rightCheekCurrent = currentLandmarks[454];
    const leftCheekBaseline = baselineLandmarks[234];
    const rightCheekBaseline = baselineLandmarks[454];

    if (!leftCheekCurrent || !rightCheekCurrent || !leftCheekBaseline || !rightCheekBaseline) {
      return 0;
    }

    // Calculate outward movement of cheeks
    const leftMovement = Math.abs(leftCheekCurrent.x - leftCheekBaseline.x);
    const rightMovement = Math.abs(rightCheekCurrent.x - rightCheekBaseline.x);

    const totalMovement = (leftMovement + rightMovement) / 2;

    console.log(`🔍 Cheek Puff Movement: Left=${leftMovement.toFixed(4)}, Right=${rightMovement.toFixed(4)}, Total=${totalMovement.toFixed(4)}`);

    return totalMovement * 1000; // Scale for better comparison
  }

  /**
   * Start peak frame tracking for an action
   */
  private startPeakTracking(actionType: string, baselineLandmarks: any[]): void {
    console.log(`🎯 Starting peak tracking for action: ${actionType}`);

    this.peakFrameTracking = {
      isTracking: true,
      currentAction: actionType,
      baselineLandmarks: baselineLandmarks ? [...baselineLandmarks] : null,
      peakMovement: 0,
      peakFrame: null,
      peakMouthMovements: null,
      peakTimestamp: 0,
      frameCount: 0,
      movementHistory: [],
      actionStartTime: Date.now()
    };
  }

  /**
   * Update peak tracking with current frame - ENHANCED DEBUGGING
   */
  private updatePeakTracking(currentLandmarks: any[]): void {
    if (!this.peakFrameTracking.isTracking || !this.peakFrameTracking.baselineLandmarks || !currentLandmarks) {
      console.log(`🎯 Peak tracking skipped: tracking=${this.peakFrameTracking.isTracking}, baseline=${!!this.peakFrameTracking.baselineLandmarks}, current=${!!currentLandmarks}`);
      return;
    }

    this.peakFrameTracking.frameCount++;

    // Calculate movement for current frame with enhanced debugging
    console.log(`🎯 PEAK TRACKING FRAME ${this.peakFrameTracking.frameCount} - Action: ${this.peakFrameTracking.currentAction}`);

    const currentMovement = this.calculateActionMovement(
      this.peakFrameTracking.currentAction,
      currentLandmarks,
      this.peakFrameTracking.baselineLandmarks
    );

    console.log(`🎯 Frame ${this.peakFrameTracking.frameCount} movement: ${currentMovement.toFixed(4)} (current peak: ${this.peakFrameTracking.peakMovement.toFixed(4)})`);

    // Add to movement history
    this.peakFrameTracking.movementHistory.push(currentMovement);

    // Keep only last 30 frames for performance
    if (this.peakFrameTracking.movementHistory.length > 30) {
      this.peakFrameTracking.movementHistory.shift();
    }

    // Check if this is a new peak
    if (currentMovement > this.peakFrameTracking.peakMovement) {
      const previousPeak = this.peakFrameTracking.peakMovement;
      this.peakFrameTracking.peakMovement = currentMovement;
      this.peakFrameTracking.peakFrame = [...currentLandmarks]; // Deep copy
      this.peakFrameTracking.peakTimestamp = Date.now();

      // Capture mouth movement measurements for smile actions
      if (this.peakFrameTracking.currentAction === 'smile') {
        const mouthMeasurements = this.analysisVisualizationService?.calculateMouthMovementMeasurements();
        if (mouthMeasurements) {
          this.peakFrameTracking.peakMouthMovements = mouthMeasurements;
          console.log(`🎯 📏 CAPTURED MOUTH MEASUREMENTS at peak frame:`);
          console.log(`   Left Vertical: ${mouthMeasurements.leftVerticalDistance.toFixed(3)}`);
          console.log(`   Right Vertical: ${mouthMeasurements.rightVerticalDistance.toFixed(3)}`);
          console.log(`   Left Horizontal: ${mouthMeasurements.leftHorizontalDistance.toFixed(3)}`);
          console.log(`   Right Horizontal: ${mouthMeasurements.rightHorizontalDistance.toFixed(3)}`);
          console.log(`   Vertical Asymmetry: ${mouthMeasurements.verticalAsymmetry.toFixed(1)}%`);
          console.log(`   Horizontal Asymmetry: ${mouthMeasurements.horizontalAsymmetry.toFixed(1)}%`);
        }
      }

      console.log(`🎯 ⭐ NEW PEAK DETECTED for ${this.peakFrameTracking.currentAction}:`);
      console.log(`   📈 Previous peak: ${previousPeak.toFixed(4)}`);
      console.log(`   📈 New peak: ${currentMovement.toFixed(4)}`);
      console.log(`   📈 Improvement: +${(currentMovement - previousPeak).toFixed(4)}`);
      console.log(`   🎬 Frame: ${this.peakFrameTracking.frameCount}`);
    } else {
      // Log when movement is lower than peak (for debugging)
      if (this.peakFrameTracking.frameCount % 5 === 0) {
        const deficit = this.peakFrameTracking.peakMovement - currentMovement;
        console.log(`🎯 Frame ${this.peakFrameTracking.frameCount}: ${currentMovement.toFixed(4)} (${deficit.toFixed(4)} below peak)`);
      }
    }

    // Enhanced periodic updates (every 10 frames)
    if (this.peakFrameTracking.frameCount % 10 === 0) {
      const elapsed = Date.now() - this.peakFrameTracking.actionStartTime;
      const recentMovements = this.peakFrameTracking.movementHistory.slice(-5);
      const avgRecent = recentMovements.reduce((sum, val) => sum + val, 0) / recentMovements.length;

      console.log(`🎯 📊 PEAK TRACKING STATUS UPDATE:`);
      console.log(`   Action: ${this.peakFrameTracking.currentAction}`);
      console.log(`   Frame: ${this.peakFrameTracking.frameCount}`);
      console.log(`   Current: ${currentMovement.toFixed(4)}`);
      console.log(`   Peak: ${this.peakFrameTracking.peakMovement.toFixed(4)}`);
      console.log(`   Recent avg: ${avgRecent.toFixed(4)}`);
      console.log(`   Elapsed: ${elapsed}ms`);
      console.log(`   Trend: ${currentMovement > avgRecent ? '📈 INCREASING' : currentMovement < avgRecent ? '📉 DECREASING' : '➡️ STABLE'}`);
    }
  }

  /**
   * Stop peak tracking and store the best frame
   */
  private stopPeakTracking(): any[] | null {
    if (!this.peakFrameTracking.isTracking) {
      return null;
    }

    const actionType = this.peakFrameTracking.currentAction;
    const peakFrame = this.peakFrameTracking.peakFrame;
    const peakMovement = this.peakFrameTracking.peakMovement;
    const frameCount = this.peakFrameTracking.frameCount;
    const elapsed = Date.now() - this.peakFrameTracking.actionStartTime;

    console.log(`🎯 PEAK TRACKING COMPLETE for ${actionType}:`);
    console.log(`   📊 Peak Movement: ${peakMovement.toFixed(2)}`);
    console.log(`   🎬 Total Frames: ${frameCount}`);
    console.log(`   ⏱️ Duration: ${elapsed}ms`);
    console.log(`   📈 Movement History: [${this.peakFrameTracking.movementHistory.map(m => m.toFixed(1)).join(', ')}]`);

    // Reset tracking
    this.peakFrameTracking = {
      isTracking: false,
      currentAction: '',
      baselineLandmarks: null,
      peakMovement: 0,
      peakFrame: null,
      peakMouthMovements: null,
      peakTimestamp: 0,
      frameCount: 0,
      movementHistory: [],
      actionStartTime: 0
    };

    return peakFrame;
  }

  /**
   * Get current peak tracking status
   */
  private getPeakTrackingStatus(): { isTracking: boolean; currentAction: string; peakMovement: number; frameCount: number } {
    return {
      isTracking: this.peakFrameTracking.isTracking,
      currentAction: this.peakFrameTracking.currentAction,
      peakMovement: this.peakFrameTracking.peakMovement,
      frameCount: this.peakFrameTracking.frameCount
    };
  }

  private initializeVisualizationServices(): void {
    console.log('[DEBUG] Initializing visualization services');
    try {
      this.visualizationService = new FaceVisualizationService('canvas');
      this.analysisVisualizationService = new AnalysisVisualizationService('analysisCanvas');
      console.log('[DEBUG] Visualization services initialized successfully');
    } catch (error) {
      console.error('[DEBUG] Failed to initialize visualization services:', error);
      throw error;
    }
  }

  /**
   * Start the facial symmetry examination process
   *
   * This is the main entry point that initializes the entire examination workflow:
   * 1. Stores patient data and navigates to exam route
   * 2. Initializes camera and visualization services
   * 3. Sets up movement detection and synkinesis detection
   * 4. Begins the examination sequence
   *
   * @param patientData - Patient information (ID, name, age)
   */
  async startExam(patientData: { id: string; name: string; age: string }): Promise<void> {
    try {
      console.log('ExamController: Starting exam for patient:', patientData);

      // Store patient data for use throughout the exam and in results
      this.patientData = patientData;
      console.log('Patient data stored in ExamController:', this.patientData);

      // CRITICAL: Navigate to exam route first to ensure proper UI state
      console.log('Navigating to exam route...');
      window.history.pushState({}, '', '/exam');

      // Wait for route change to take effect and DOM to update
      await new Promise(resolve => setTimeout(resolve, 100));

      // Initialize visualization services now that we're in the exam view
      this.initializeVisualizationServices();

      // Set up exam button listeners now that the exam view is loaded
      this.setupExamButtonListeners();

      // Ensure camera processing is stopped before starting
      if (this.isProcessing) {
        this.isProcessing = false;
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      const videoElement = document.getElementById('video') as HTMLVideoElement;
      console.log('ExamController: Video element found:', videoElement);

      if (!videoElement) {
        console.error('ExamController: Video element not found!');
        throw new Error('Video element not found');
      }

      // Ensure video element is visible and properly configured
      videoElement.style.display = 'block';
      videoElement.style.visibility = 'visible';
      videoElement.style.opacity = '1';
      console.log('Video element visibility ensured');
      // Debug: Log srcObject and video state
      setTimeout(() => {
        console.log('[DEBUG] video.srcObject:', videoElement.srcObject);
        console.log('[DEBUG] video.readyState:', videoElement.readyState);
        console.log('[DEBUG] video.paused:', videoElement.paused);
        console.log('[DEBUG] video.videoWidth:', videoElement.videoWidth);
        console.log('[DEBUG] video.videoHeight:', videoElement.videoHeight);
      }, 1000);

      // Clear any existing video source
      if (videoElement.srcObject) {
        const stream = videoElement.srcObject as MediaStream;
        stream.getTracks().forEach(track => track.stop());
        videoElement.srcObject = null;
      }

      // Clear all canvases before starting (with null checks)
      this.visualizationService?.clearCanvas();
      this.analysisVisualizationService?.clearCanvas();

      // Reset movement detection service for new exam
      this.movementDetectionService.reset();
      this.detectionStatusView.reset();

      // 🔬 Reset advanced synkinesis detection service for new exam
      this.synkinesisDetectionService.reset();

      // Set up camera results handler BEFORE starting the exam
      this.cameraRepository.onResults((results) => {
        this.handleCameraResults(results);
      });

      // Reinitialize visualization services with fresh video element
      this.analysisVisualizationService?.setVideoElement(videoElement);

      const session = await this.examOrchestrator.startExam({
        patientData,
        videoElement
      });

      // Wait a moment for camera and MediaPipe to fully initialize
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Camera initialization delay completed');



      // Show exam interface and update UI
      this.showExamInterface();
      this.updateUI();

      // Initialize camera switching functionality
      await this.initializeCameraSwitching();

      // Ensure instruction element is visible and has content
      const instructionElement = document.getElementById('instruction');
      if (instructionElement) {
        instructionElement.style.display = 'block';
        const welcomeMessage = 'Examination started. Follow the instructions for each facial action.';
        instructionElement.textContent = welcomeMessage;
      }

      // Reset speech state for new exam (do not speak here)
      this.speechService.resetSpeechState();

      // Set last spoken text to the first instruction to prevent double speaking
      const firstAction = this.examOrchestrator.getCurrentAction();
      if (firstAction && firstAction.name && firstAction.instruction) {
        const firstSpeech = this.getShortSpeechInstruction(firstAction.name, firstAction.instruction);
        this.speechService.setLastSpokenText(firstSpeech);
      }

      console.log('Exam started successfully');
    } catch (error) {
      console.error('Failed to start exam:', error);
      this.showErrorMessage('Failed to start examination. Please check camera permissions and try again.');
    }
  }

  /**
   * Handle camera results from MediaPipe FaceMesh detection
   *
   * This is the core real-time processing method that:
   * 1. Validates and processes facial landmarks (468+ points)
   * 2. Performs movement detection and peak tracking
   * 3. Runs advanced synkinesis detection with clinical validation
   * 4. Updates visualization and UI components
   * 5. Manages examination flow and action progression
   *
   * Called for every camera frame with detected faces.
   *
   * @param results - MediaPipe detection results containing facial landmarks
   */
  private async handleCameraResults(results: any): Promise<void> {
    // Prevent concurrent processing of multiple frames (performance optimization)
    if (this.isProcessing) return;

    try {
      this.isProcessing = true;

      console.log('Camera results received:', results); // Debug log

      // === FACE DETECTION AND LANDMARK PROCESSING ===
      if (results.multiFaceLandmarks && results.multiFaceLandmarks.length > 0) {
        const landmarks = results.multiFaceLandmarks[0];
        console.log('Processing landmarks:', landmarks.length, 'points'); // Debug log

        // === LANDMARK VALIDATION ===
        // Validate MediaPipe landmark data structure
        if (!landmarks || !Array.isArray(landmarks)) {
          console.error('Invalid landmark data received from MediaPipe:', landmarks);
          return;
        }

        // Check landmark count (MediaPipe FaceMesh: 468 standard, 478 with iris)
        if (landmarks.length !== 468) {
          console.warn(`MediaPipe returned ${landmarks.length} landmarks instead of expected 468`);
          // Continue processing but log the issue for debugging
        }

        // Validate landmark structure
        const validLandmarks = landmarks.filter(landmark =>
          landmark &&
          typeof landmark.x === 'number' &&
          typeof landmark.y === 'number'
        );

        if (validLandmarks.length !== landmarks.length) {
          console.error(`Found ${landmarks.length - validLandmarks.length} invalid landmarks in MediaPipe data`);
          // Don't process invalid data
          return;
        }

        console.log(`✓ MediaPipe landmark validation passed: ${validLandmarks.length} valid landmarks`);

        // Perform movement detection for current action
        const currentAction = this.examOrchestrator.getCurrentAction();
        if (currentAction) {
          const actionType = this.mapActionToType(currentAction.name);
          const detectionResult = this.movementDetectionService.detectMovement(actionType, validLandmarks);

          // Update detection status UI
          this.detectionStatusView.updateDetectionStatus(actionType, detectionResult);
          this.detectionStatusView.updateCurrentAction(actionType, currentAction.instruction);

          // 🎯 PEAK FRAME TRACKING INTEGRATION - ENHANCED BASELINE VALIDATION
          // Set baseline if this is the first action
          if (actionType === 'baseline' && detectionResult.isDetected) {
            this.movementDetectionService.setBaseline(validLandmarks);
            // Store baseline for peak tracking with validation
            this.peakFrameTracking.baselineLandmarks = [...validLandmarks];

            // 🔬 ADVANCED SYNKINESIS DETECTION - Set baseline
            this.synkinesisDetectionService.setBaseline(validLandmarks);

            console.log('🎯 ✅ BASELINE CAPTURED FOR PEAK TRACKING:');
            console.log(`   📊 Landmark count: ${validLandmarks.length}`);

            // Validate critical mouth landmarks for normal person testing
            const leftCorner = validLandmarks[61];
            const rightCorner = validLandmarks[291];
            const normalizationLandmark1 = validLandmarks[33];
            const normalizationLandmark2 = validLandmarks[263];

            console.log(`   👄 Mouth landmarks - Left corner (61): (${leftCorner?.x?.toFixed(6)}, ${leftCorner?.y?.toFixed(6)})`);
            console.log(`   👄 Mouth landmarks - Right corner (291): (${rightCorner?.x?.toFixed(6)}, ${rightCorner?.y?.toFixed(6)})`);
            console.log(`   📏 Normalization landmarks - 33: (${normalizationLandmark1?.x?.toFixed(6)}, ${normalizationLandmark1?.y?.toFixed(6)})`);
            console.log(`   📏 Normalization landmarks - 263: (${normalizationLandmark2?.x?.toFixed(6)}, ${normalizationLandmark2?.y?.toFixed(6)})`);

            // Calculate baseline mouth width for reference
            if (leftCorner && rightCorner) {
              const baselineMouthWidth = Math.sqrt(
                Math.pow(rightCorner.x - leftCorner.x, 2) +
                Math.pow(rightCorner.y - leftCorner.y, 2)
              );
              console.log(`   📐 Baseline mouth width: ${baselineMouthWidth.toFixed(6)} (${(baselineMouthWidth * 1000).toFixed(2)}mm equivalent)`);
            }

            // Calculate normalization factor for reference
            if (normalizationLandmark1 && normalizationLandmark2) {
              const normalizationFactor = Math.sqrt(
                Math.pow(normalizationLandmark2.x - normalizationLandmark1.x, 2) +
                Math.pow(normalizationLandmark2.y - normalizationLandmark1.y, 2)
              );
              console.log(`   📏 Normalization factor: ${normalizationFactor.toFixed(6)}`);
              console.log(`   ✅ Baseline quality: ${(normalizationFactor > 0.08 && normalizationFactor < 0.4) ? 'GOOD' : 'QUESTIONABLE'}`);
            }
          }

          // Start peak tracking for non-baseline actions when movement is first detected
          if (actionType !== 'baseline' && detectionResult.isDetected && !this.peakFrameTracking.isTracking) {
            if (this.peakFrameTracking.baselineLandmarks) {
              this.startPeakTracking(actionType, this.peakFrameTracking.baselineLandmarks);
            }
          }

          // Update peak tracking for ongoing actions
          if (this.peakFrameTracking.isTracking && this.peakFrameTracking.currentAction === actionType) {
            this.updatePeakTracking(validLandmarks);
          }

          // Update button states based on detection
          this.updateButtonStatesWithDetection();
        }

        // Store landmarks for clinical analysis
        this.storeLandmarksForAnalysis(landmarks);

        // 🔬 ADVANCED SYNKINESIS DETECTION - Process current frame
        const synkinesisEvents = this.synkinesisDetectionService.processFrame(landmarks, Date.now());
        if (synkinesisEvents.length > 0) {
          console.log(`🔬 ⚠️ SYNKINESIS DETECTED: ${synkinesisEvents.length} events`);
          synkinesisEvents.forEach(event => {
            console.log(`   Type: ${event.type}, Side: ${event.side}, Severity: ${event.severity}, Confidence: ${event.metrics.confidence.toFixed(2)}`);
          });
        }

        // Draw enhanced landmarks with face circle
        this.visualizationService?.drawFaceLandmarks(landmarks);

        // Update analysis visualization
        console.log('Updating analysis visualization with landmarks:', landmarks.length);
        this.analysisVisualizationService?.updateLandmarks(landmarks);

        // Note: Action visualization removed - Action View no longer exists

        // Note: Action overlay removed - instructions now shown in dedicated area below cameras

        // Highlight relevant landmarks for current metric
        this.highlightRelevantLandmarks();

        // Process facial data
        await this.examOrchestrator.processFacialData(landmarks);

        this.updateUI();
      } else {
        console.log('No face landmarks detected'); // Debug log
      }
    } catch (error) {
      console.error('Error processing facial data:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  async nextAction(): Promise<void> {
    // Check if current movement is detected before allowing progression
    const currentAction = this.examOrchestrator.getCurrentAction();
    if (currentAction) {
      const actionType = this.mapActionToType(currentAction.name);
      const detectionStatus = this.movementDetectionService.getDetectionStatus();

      if (!detectionStatus.get(actionType)) {
        this.detectionStatusView.showError('Please complete the current movement before proceeding.');
        return;
      }

      // 🎯 STOP PEAK TRACKING AND STORE BEST FRAME - ENHANCED DEBUGGING
      if (this.peakFrameTracking.isTracking && this.peakFrameTracking.currentAction === actionType) {
        console.log(`🎯 🔄 FINALIZING PEAK TRACKING for ${actionType}:`);
        console.log(`   📊 Total frames processed: ${this.peakFrameTracking.frameCount}`);
        console.log(`   📈 Peak movement achieved: ${this.peakFrameTracking.peakMovement.toFixed(4)}`);
        console.log(`   📈 Movement history: [${this.peakFrameTracking.movementHistory.slice(-10).map(m => m.toFixed(2)).join(', ')}]`);

        const bestFrame = this.stopPeakTracking();
        if (bestFrame) {
          // Store the peak frame instead of the current frame
          console.log(`🎯 ✅ STORING PEAK FRAME for ${actionType}:`);
          console.log(`   🎬 Frame landmarks: ${bestFrame.length}`);

          // Validate peak frame quality for normal person testing
          if (actionType === 'smile') {
            const leftCorner = bestFrame[61];
            const rightCorner = bestFrame[291];
            const baseline = this.peakFrameTracking.baselineLandmarks;

            if (leftCorner && rightCorner && baseline) {
              const leftCornerBaseline = baseline[61];
              const rightCornerBaseline = baseline[291];

              console.log(`🎯 📊 PEAK FRAME SMILE VALIDATION:`);
              console.log(`   Left corner movement: (${leftCornerBaseline?.x?.toFixed(6)}, ${leftCornerBaseline?.y?.toFixed(6)}) → (${leftCorner.x.toFixed(6)}, ${leftCorner.y.toFixed(6)})`);
              console.log(`   Right corner movement: (${rightCornerBaseline?.x?.toFixed(6)}, ${rightCornerBaseline?.y?.toFixed(6)}) → (${rightCorner.x.toFixed(6)}, ${rightCorner.y.toFixed(6)})`);

              const leftDx = leftCorner.x - leftCornerBaseline.x;
              const leftDy = leftCornerBaseline.y - leftCorner.y;
              const rightDx = rightCorner.x - rightCornerBaseline.x;
              const rightDy = rightCornerBaseline.y - rightCorner.y;

              console.log(`   Left displacement: dx=${leftDx.toFixed(6)}, dy=${leftDy.toFixed(6)}`);
              console.log(`   Right displacement: dx=${rightDx.toFixed(6)}, dy=${rightDy.toFixed(6)}`);
              console.log(`   Expected for normal smile: Both corners should move outward (positive dx) and upward (positive dy)`);
              console.log(`   Left direction: ${leftDx > 0 ? '✅ OUTWARD' : '❌ INWARD'} and ${leftDy > 0 ? '✅ UPWARD' : '❌ DOWNWARD'}`);
              console.log(`   Right direction: ${rightDx > 0 ? '✅ OUTWARD' : '❌ INWARD'} and ${rightDy > 0 ? '✅ UPWARD' : '❌ DOWNWARD'}`);
            }
          }

          this.storePeakFrameLandmarks(bestFrame, actionType);
        } else {
          console.warn(`🎯 ❌ No peak frame found for ${actionType} - this indicates a problem with peak tracking`);
          console.warn(`   Possible issues: No movement detected, tracking not started, or calculation errors`);
        }
      } else {
        console.log(`🎯 ⚠️ Peak tracking not active for ${actionType}: tracking=${this.peakFrameTracking.isTracking}, currentAction=${this.peakFrameTracking.currentAction}`);
      }
    }

    const hasNext = await this.examOrchestrator.nextAction();

    if (!hasNext || this.examOrchestrator.isExamCompleted()) {
      this.showResults();
    } else {
      this.updateUI();
      // Reset detection status view for new action
      const newAction = this.examOrchestrator.getCurrentAction();
      if (newAction) {
        const newActionType = this.mapActionToType(newAction.name);
        this.detectionStatusView.updateCurrentAction(newActionType, newAction.instruction);
      }
    }
  }

  cycleAnalysisMode(): void {
    this.analysisVisualizationService?.cycleAnalysisMode();
  }


  /**
   * Smart speech function that prevents unnecessary interruptions
   */

  /**
   * Perform the actual speech synthesis with state tracking
   */

  /**
   * Get ultra-short speech commands for instant delivery
   */
  private getShortSpeechInstruction(actionName: string, instruction: string): string {
    console.log('getShortSpeechInstruction called with actionName:', actionName);
    console.log('getShortSpeechInstruction called with instruction:', instruction);

    const quickCommands: Record<string, string> = {
      // Direct action name matches - Full sentences for better clarity
      'neutral': 'Please relax your face and maintain a neutral expression',
      'eyebrow_raise': 'Please raise both eyebrows as high as possible',
      'eye_close': 'Please close your eyes tightly',
      'smile': 'Please show your biggest smile',
      'lip_pucker': 'Please pucker your lips as if you are going to whistle',
      'cheek_puff': 'Please puff out your cheeks with air',

      // Alternative action name formats
      'neutral_face': 'Please relax your face and maintain a neutral expression',
      'eyebrow_elevation': 'Please raise both eyebrows as high as possible',
      'gentle_eye_closure': 'Please close your eyes tightly',
      'show_teeth': 'Please show your biggest smile',
      'pucker_lips': 'Please pucker your lips as if you are going to whistle',
      'blow_cheeks': 'Please puff out your cheeks with air',

      // Common instruction-based fallbacks
      'Neutral Face': 'Please relax your face and maintain a neutral expression',
      'Eyebrow Elevation': 'Please raise both eyebrows as high as possible',
      'Gentle Eye Closure': 'Please close your eyes tightly',
      'Show Teeth': 'Please show your biggest smile',
      'Pucker Lips': 'Please pucker your lips as if you are going to whistle',
      'Blow Cheeks': 'Please puff out your cheeks with air'
    };

    // Try direct match first
    let speechCommand = quickCommands[actionName];

    if (!speechCommand) {
      // Try case-insensitive match
      const lowerActionName = actionName.toLowerCase();
      speechCommand = quickCommands[lowerActionName];
    }

    if (!speechCommand) {
      // Try matching against instruction text
      speechCommand = quickCommands[instruction];
    }

    if (!speechCommand) {
      // Try partial matching for common patterns - Full sentences
      const lowerActionName = actionName.toLowerCase();
      if (lowerActionName.includes('eyebrow') || lowerActionName.includes('raise')) {
        speechCommand = 'Please raise both eyebrows as high as possible';
      } else if (lowerActionName.includes('eye') && lowerActionName.includes('close')) {
        speechCommand = 'Please close your eyes tightly';
      } else if (lowerActionName.includes('smile') || lowerActionName.includes('teeth')) {
        speechCommand = 'Please show your biggest smile';
      } else if (lowerActionName.includes('pucker') || lowerActionName.includes('lips')) {
        speechCommand = 'Please pucker your lips as if you are going to whistle';
      } else if (lowerActionName.includes('cheek') || lowerActionName.includes('puff')) {
        speechCommand = 'Please puff out your cheeks with air';
      } else if (lowerActionName.includes('neutral') || lowerActionName.includes('relax')) {
        speechCommand = 'Please relax your face and maintain a neutral expression';
      }
    }

    // Final fallback - use the instruction text itself if it's short enough
    if (!speechCommand) {
      if (instruction && instruction.length <= 20) {
        speechCommand = instruction;
      } else {
        speechCommand = 'Follow the instruction';
      }
    }

    console.log('Final speech command:', speechCommand);
    return speechCommand;
  }

  /**
   * Stop any currently playing speech and reset state
   */

  /**
   * Reset speech state for new examination
   */

  /**
   * Test speech synthesis functionality
   */
  public testSpeech(): void {
    console.log('Testing speech synthesis...');
    // SpeechService handles initialization internally if needed
    this.speechService.speakInstruction('Speech synthesis test is working correctly. You should be able to hear clear audio instructions during the examination.');
  }

  /**
   * Toggle speech synthesis on/off
   */
  public toggleSpeech(): void {
    // Implement speech toggle using SpeechService if needed
    // Example: this.speechService.toggleSpeech();
  }

  private setupEventListeners(): void {
    // Note: This is called during constructor when exam buttons don't exist yet
    // The actual exam button listeners are set up in setupExamButtonListeners()
    // after the exam view is loaded
  }

  private setupExamButtonListeners(): void {
    console.log('[DEBUG] Setting up exam button listeners');

    // Next action button
    const nextButton = document.getElementById('nextBtn');
    if (nextButton) {
      nextButton.addEventListener('click', () => {
        console.log('[DEBUG] Next button clicked');
        this.nextAction();
      });
      console.log('[DEBUG] Next button listener attached');
    } else {
      console.log('[DEBUG] Next button not found');
    }

    // Analysis mode cycle button
    const cycleAnalysisButton = document.getElementById('cycleAnalysisBtn');
    if (cycleAnalysisButton) {
      cycleAnalysisButton.addEventListener('click', () => {
        console.log('[DEBUG] Cycle analysis button clicked');
        this.cycleAnalysisMode();
      });
      console.log('[DEBUG] Cycle analysis button listener attached');
    } else {
      console.log('[DEBUG] Cycle analysis button not found');
    }

    // Setup other exam buttons
    this.attachFinishButtonListener();
    this.attachSpeechToggleListener();
    this.attachTestSpeechListener();
    this.attachCameraSwitchListener();

    // Export buttons (for results view)
    const csvButton = document.getElementById('exportCsv');
    // Note: CSV and Markdown export buttons have been replaced with PDF export
    // These event listeners are kept for backward compatibility but should be removed
    if (csvButton) {
      csvButton.addEventListener('click', () => {
        console.log('[DEBUG] Export CSV button clicked - redirecting to PDF export');
        this.exportResults();
      });
      console.log('[DEBUG] Export CSV button listener attached (redirects to PDF)');
    }

    const markdownButton = document.getElementById('exportMarkdown');
    if (markdownButton) {
      markdownButton.addEventListener('click', () => {
        console.log('[DEBUG] Export Markdown button clicked - redirecting to PDF export');
        this.exportResults();
      });
      console.log('[DEBUG] Export Markdown button listener attached (redirects to PDF)');
    }
  }

  private attachSpeechToggleListener(): void {
    const speechToggleBtn = document.getElementById('speechToggleBtn');
    if (speechToggleBtn) {
      speechToggleBtn.addEventListener('click', () => {
        this.toggleSpeech();
        this.updateSpeechToggleButton();
      });
      console.log('Speech toggle button listener attached');
    }
  }

  private attachTestSpeechListener(): void {
    const testSpeechBtn = document.getElementById('testSpeechBtn');
    if (testSpeechBtn) {
      testSpeechBtn.addEventListener('click', () => {
        console.log('Test speech button clicked');
        this.testSpeech();
      });
      console.log('Test speech button listener attached');
    }
  }

  private attachCameraSwitchListener(): void {
    const switchCameraBtn = document.getElementById('switchCameraBtn');
    if (switchCameraBtn) {
      switchCameraBtn.addEventListener('click', async () => {
        console.log('Switch camera button clicked');
        await this.switchCamera();
      });
      console.log('Switch camera button listener attached');
    }
  }

  private updateSpeechToggleButton(): void {
    const speechToggleBtn = document.getElementById('speechToggleBtn');
    if (speechToggleBtn) {
      if (this.speechService && (this.speechService as any).speechEnabled) {
        speechToggleBtn.textContent = '🔊 Speech ON';
        speechToggleBtn.style.background = '#e67e22'; // Orange when on
        speechToggleBtn.title = 'Click to disable speech instructions';
      } else {
        speechToggleBtn.textContent = '🔇 Speech OFF';
        speechToggleBtn.style.background = '#7f8c8d'; // Gray when off
        speechToggleBtn.title = 'Click to enable speech instructions';
      }
    }
  }

  private attachFinishButtonListener(): void {
    const finishBtn = document.getElementById('finishBtn');
    if (finishBtn) {
      // Remove any existing listeners to prevent duplicates
      finishBtn.removeEventListener('click', this.handleFinishClick);
      // Add the listener
      finishBtn.addEventListener('click', this.handleFinishClick);
      console.log('Finish button listener attached successfully');
    } else {
      console.log('Finish button not found in DOM');
    }
  }

  private handleFinishClick = (): void => {
    console.log('Finish button clicked!');
    this.finishExam();
  }

  private updateButtonStates(): void {
    const currentAction = this.getCurrentActionType();
    const nextBtn = document.getElementById('nextBtn') as HTMLButtonElement;
    const finishBtn = document.getElementById('finishBtn') as HTMLButtonElement;

    console.log('Updating button states for action:', currentAction);

    // Check if this is the final action (Smile Wide)
    if (currentAction === 'smile' || this.examOrchestrator.isExamCompleted()) {
      // Final action - show finish button, hide next button
      if (nextBtn) {
        nextBtn.style.display = 'none';
        console.log('Next button hidden - final action reached');
      }
      if (finishBtn) {
        finishBtn.style.display = 'inline-block';
        finishBtn.textContent = 'Finish Examination';
        finishBtn.className = 'btn btn-success btn-lg'; // Green styling for completion
        finishBtn.style.backgroundColor = '#28a745';
        finishBtn.style.borderColor = '#28a745';
        finishBtn.style.color = 'white';
        finishBtn.style.fontWeight = 'bold';
        console.log('Finish Examination button shown with green styling');

        // Reattach event listener when button becomes visible
        this.attachFinishButtonListener();
      }
    } else {
      // Regular actions - show next button, hide finish button
      if (nextBtn) {
        nextBtn.style.display = 'inline-block';
        nextBtn.textContent = 'Next Action';
        nextBtn.className = 'btn btn-primary btn-lg'; // Standard blue styling
        console.log('Next button shown');
      }
      if (finishBtn) {
        finishBtn.style.display = 'none';
        console.log('Finish button hidden');
      }
    }
  }

  private async finishExam(): Promise<void> {
    try {
      console.log('Starting finishExam() method...');

      // Check if all movements are detected before finishing
      const allDetected = this.movementDetectionService.areAllMovementsDetected();
      if (!allDetected) {
        this.detectionStatusView.showError('Please complete all facial movements before finishing the examination.');
        return;
      }

      // 🎯 STOP PEAK TRACKING FOR FINAL ACTION
      if (this.peakFrameTracking.isTracking) {
        const currentAction = this.examOrchestrator.getCurrentAction();
        if (currentAction) {
          const actionType = this.mapActionToType(currentAction.name);
          const bestFrame = this.stopPeakTracking();
          if (bestFrame) {
            console.log(`🎯 Storing FINAL PEAK FRAME for ${actionType} with ${bestFrame.length} landmarks`);
            this.storePeakFrameLandmarks(bestFrame, actionType);
          }
        }
      }

      // Debug: Log landmark storage state before finishing
      const debugLandmarkKeys = ['baseline', 'eyebrowRaise', 'eyeClose', 'smile', 'lipPucker', 'cheekPuff'];
      debugLandmarkKeys.forEach(key => {
        const arr = this.landmarkStorage.get(key);
        console.log(`[DEBUG] Landmark storage for ${key}:`, arr ? arr.length : 0, 'points');
      });

      // Stop camera processing
      this.isProcessing = false;
      console.log('Camera processing stopped');

      // CRITICAL: Stop all speech and reset state before completion
      console.log('Stopping all speech and resetting speech state...');
      this.speechService.stopCurrentSpeech();
      this.speechService.resetSpeechState();

      // Wait a moment to ensure all speech is fully stopped
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log('Speech fully stopped and state reset');

      // Collect final results
      console.log('Collecting examination results...');
      this.examResults = await this.collectExamResults();
      this.examCompleted = true;
      console.log('Results collected:', this.examResults);

      // Store results for the dedicated results route
      console.log('Storing results for results route...');
      this.storeResultsForRoute();

      // Navigate to the dedicated results route
      console.log('Navigating to results route...');
      this.navigateToResults();

      // Wait another moment before speaking completion message to ensure clean state
      setTimeout(() => {
        console.log('Speaking completion message...');
        this.speechService.speakInstruction('Facial symmetry examination is now complete. Your results are ready for review.');
      }, 1000); // 1 second delay to ensure everything is settled

      console.log('Navigation to results route completed');

    } catch (error) {
      console.error('Error finishing exam:', error);
      alert('Error completing examination. Please try again.');
    }
  }

  private async collectExamResults(): Promise<any> {
    // Collect comprehensive examination results
    const currentSession = this.examOrchestrator.getCurrentSession();

    // Use stored patient data instead of trying to get it from DOM elements
    let patientId = this.patientData?.id || '';
    let patientName = this.patientData?.name || '';
    let patientAge = this.patientData?.age || '';

    // Fallback: Try to get patient data from the current session if stored data is missing
    if (!patientId && !patientName && !patientAge && currentSession) {
      const sessionPatient = currentSession.patient;
      if (sessionPatient) {
        patientId = sessionPatient.id || '';
        patientName = sessionPatient.name || '';
        patientAge = sessionPatient.age?.toString() || '';
        console.log('Using patient data from session as fallback:', {
          id: patientId,
          name: patientName,
          age: patientAge
        });
      }
    }

    console.log('Final patient data for results:', {
      id: patientId,
      name: patientName,
      age: patientAge
    });

    // Debug landmark storage state before analysis
    console.log('=== LANDMARK STORAGE DEBUG ===');
    console.log('Landmark storage size:', this.landmarkStorage.size);
    console.log('Landmark storage keys:', Array.from(this.landmarkStorage.keys()));

    // Log detailed landmark counts
    const landmarkCounts: { [key: string]: number } = {};
    for (const [key, landmarks] of this.landmarkStorage.entries()) {
      landmarkCounts[key] = landmarks ? landmarks.length : 0;
    }

    console.log('Landmark counts by action:', landmarkCounts);

    // Perform clinical analysis using real landmark data if available
    try {
      if (this.landmarkStorage.size > 0) {
        console.log('Performing clinical analysis with real landmark data...');
        const clinicalData = await this.prepareRealClinicalExaminationData(patientId, patientName, patientAge);
        if (clinicalData) {
          this.clinicalResults = await this.clinicalIntegrationService.performClinicalAnalysis(clinicalData);
          console.log('Clinical analysis completed:', this.clinicalResults);
        } else {
          console.error('❌ ERROR: Clinical data preparation failed - no real landmark data available');
          throw new Error('Clinical analysis failed: Unable to prepare clinical data from landmark storage');
        }

        // Perform comparison analysis using real landmark data
        console.log('Performing comparison analysis with real landmark data...');
        const comparisonData = await this.prepareRealComparisonExaminationData(patientId, patientName, patientAge);
        if (comparisonData) {
          this.comparisonResults = await this.clinicalComparisonService.performClinicalComparison(comparisonData);
          console.log('Comparison analysis completed:', this.comparisonResults);
        } else {
          console.error('❌ ERROR: Comparison data preparation failed - no real landmark data available');
          throw new Error('Comparison analysis failed: Unable to prepare comparison data from landmark storage');
        }
      } else {
        console.error('❌ ERROR: No landmark data available for clinical analysis');
        throw new Error('Clinical analysis failed: No facial landmark data available. Please complete the examination first.');
      }
    } catch (error) {
      console.error('Clinical analysis failed:', error);
      // Continue with basic analysis if clinical analysis fails
    }

    return {
      patientInfo: {
        id: patientId,
        name: patientName,
        age: patientAge
      },
      timestamp: new Date().toISOString(),
      actions: this.getCompletedActionsList(),
      symmetryMetrics: this.calculateSymmetryMetrics(),
      asymmetries: this.detectAsymmetries(),
      overallScore: this.calculateOverallScore(),
      clinicalAnalysis: this.clinicalResults,
      comparisonAnalysis: this.comparisonResults
    };
  }

  private getCompletedActionsList(): string[] {
    // Return list of completed actions for the streamlined 4-action protocol
    return [
      'Neutral / Resting Face',
      'Raise Eyebrows',
      'Close Eyes Tightly',
      'Smile Wide'
    ];
  }

  /**
   * Prepare clinical examination data from current session
   */
  private async prepareClinicalExaminationData(
    patientId: string,
    patientName: string,
    patientAge: string
  ): Promise<ClinicalExaminationData | null> {

    // Get current session and landmark data
    const currentSession = this.examOrchestrator.getCurrentSession();
    if (!currentSession) {
      console.log('No current session available for clinical analysis');
      return null;
    }

    // No mock data - throw error if real landmark data is not available
    throw new Error('Mock data generation removed - only real landmark data is supported');
  }

  /**
   * Prepare comparison examination data from current session
   */
  private async prepareComparisonExaminationData(
    patientId: string,
    patientName: string,
    patientAge: string
  ): Promise<ComparisonExaminationData | null> {

    // Get current session and landmark data
    const currentSession = this.examOrchestrator.getCurrentSession();
    if (!currentSession) {
      console.log('No current session available for comparison analysis');
      return null;
    }

    // No mock data - throw error if real landmark data is not available
    throw new Error('Mock data generation removed - only real landmark data is supported');
  }

  /**
   * Prepare clinical examination data using real landmark data
   */
  private async prepareRealClinicalExaminationData(
    patientId: string,
    patientName: string,
    patientAge: string
  ): Promise<ClinicalExaminationData | null> {

    if (this.landmarkStorage.size === 0) {
      console.log('No real landmark data available for clinical analysis');
      return null;
    }

    // Convert stored landmarks to the format expected by clinical analysis
    // Note: Removed lipPucker and cheekPuff as per user preference
    const realLandmarkData = {
      baseline: this.landmarkStorage.get('baseline') || [],
      eyebrowRaise: this.landmarkStorage.get('eyebrowRaise') || [],
      eyeClose: this.landmarkStorage.get('eyeClose') || [],
      smile: this.landmarkStorage.get('smile') || []
    };

    // Validate all landmark data before proceeding
    const validationErrors: string[] = [];
    const requiredActions = ['baseline', 'eyebrowRaise', 'eyeClose', 'smile']; // Removed lipPucker and cheekPuff
    const expectedLandmarkCounts = [468, 478]; // Support both MediaPipe v1 and v2

    for (const action of requiredActions) {
      const landmarks = realLandmarkData[action as keyof typeof realLandmarkData];
      if (!landmarks || !Array.isArray(landmarks)) {
        validationErrors.push(`Missing landmark data for ${action}`);
      } else if (!expectedLandmarkCounts.includes(landmarks.length)) {
        validationErrors.push(`Invalid landmark count for ${action}: ${landmarks.length}/${expectedLandmarkCounts.join(' or ')}`);
      } else {
        // Validate landmark structure
        const invalidLandmarks = landmarks.filter((landmark) => {
          return !landmark ||
                 typeof landmark.x !== 'number' ||
                 typeof landmark.y !== 'number';
        });
        if (invalidLandmarks.length > 0) {
          validationErrors.push(`Invalid landmark structure for ${action}: ${invalidLandmarks.length} invalid points`);
        }
      }
    }

    if (validationErrors.length > 0) {
      console.error('Clinical data validation failed:', validationErrors);
      console.error('Cannot proceed with clinical analysis due to invalid landmark data');
      return null;
    }

    const clinicalData: ClinicalExaminationData = {
      patientInfo: {
        id: patientId,
        name: patientName,
        age: parseInt(patientAge) || 0,
        affectedSide: 'unknown'
      },
      landmarkData: realLandmarkData,
      timestamp: new Date().toISOString()
    };

    console.log('✓ Clinical examination data validation passed');
    console.log('Prepared real clinical examination data with landmark counts:', {
      baseline: realLandmarkData.baseline.length,
      eyebrowRaise: realLandmarkData.eyebrowRaise.length,
      eyeClose: realLandmarkData.eyeClose.length,
      smile: realLandmarkData.smile.length
    });

    return clinicalData;
  }

  /**
   * Prepare comparison examination data using real landmark data
   */
  private async prepareRealComparisonExaminationData(
    patientId: string,
    patientName: string,
    patientAge: string
  ): Promise<ComparisonExaminationData | null> {

    if (this.landmarkStorage.size === 0) {
      console.log('No real landmark data available for comparison analysis');
      return null;
    }

    // Convert stored landmarks to the format expected by comparison analysis
    // Note: Removed lipPucker and cheekPuff as per user preference
    const realLandmarkData = {
      baseline: this.landmarkStorage.get('baseline') || [],
      eyebrowRaise: this.landmarkStorage.get('eyebrowRaise') || [],
      eyeClose: this.landmarkStorage.get('eyeClose') || [],
      smile: this.landmarkStorage.get('smile') || []
    };

    // Validate all landmark data before proceeding (same validation as clinical data)
    const validationErrors: string[] = [];
    const requiredActions = ['baseline', 'eyebrowRaise', 'eyeClose', 'smile']; // Removed lipPucker and cheekPuff
    const expectedLandmarkCounts = [468, 478]; // Support both MediaPipe v1 and v2

    for (const action of requiredActions) {
      const landmarks = realLandmarkData[action as keyof typeof realLandmarkData];
      if (!landmarks || !Array.isArray(landmarks)) {
        validationErrors.push(`Missing landmark data for ${action}`);
      } else if (!expectedLandmarkCounts.includes(landmarks.length)) {
        validationErrors.push(`Invalid landmark count for ${action}: ${landmarks.length}/${expectedLandmarkCounts.join(' or ')}`);
      } else {
        // Validate landmark structure
        const invalidLandmarks = landmarks.filter((landmark) => {
          return !landmark ||
                 typeof landmark.x !== 'number' ||
                 typeof landmark.y !== 'number';
        });
        if (invalidLandmarks.length > 0) {
          validationErrors.push(`Invalid landmark structure for ${action}: ${invalidLandmarks.length} invalid points`);
        }
      }
    }

    if (validationErrors.length > 0) {
      console.error('Comparison data validation failed:', validationErrors);
      console.error('Cannot proceed with comparison analysis due to invalid landmark data');
      return null;
    }

    const comparisonData: ComparisonExaminationData = {
      patientInfo: {
        id: patientId,
        name: patientName,
        age: parseInt(patientAge) || 0
      },
      landmarkData: realLandmarkData,
      timestamp: new Date().toISOString()
    };

    console.log('✓ Comparison examination data validation passed');
    console.log('Prepared real comparison examination data with landmark counts:', {
      baseline: realLandmarkData.baseline.length,
      eyebrowRaise: realLandmarkData.eyebrowRaise.length,
      eyeClose: realLandmarkData.eyeClose.length,
      smile: realLandmarkData.smile.length
    });

    return comparisonData;
  }

  // Mock landmark data generation removed - only real data is used

  private calculateSymmetryMetrics(): any {
    console.log('=== CALCULATING SYMMETRY METRICS ===');
    console.log('Clinical results available:', !!this.clinicalResults);
    console.log('Landmark storage size:', this.landmarkStorage.size);
    console.log('Landmark storage keys:', Array.from(this.landmarkStorage.keys()));

    // Use real clinical analysis if available
    if (this.clinicalResults) {
      console.log('Using clinical analysis results');
      const regional = this.clinicalResults.regional_analysis;
      return {
        // Individual measurements for detailed analysis
        leftEyebrowElevation: this.calculateEyebrowElevation('left'),
        rightEyebrowElevation: this.calculateEyebrowElevation('right'),
        eyebrowAsymmetry: regional.forehead.asymmetry_percentage,

        leftEyeClosure: this.calculateEyeClosure('left'),
        rightEyeClosure: this.calculateEyeClosure('right'),
        eyeAsymmetry: regional.eye.asymmetry_percentage,

        leftMouthMovement: this.calculateMouthMovement('left'),
        rightMouthMovement: this.calculateMouthMovement('right'),
        mouthAsymmetry: regional.smile.asymmetry_percentage,
        commissureDroop: regional.smile.commissure_droop || 0,

        // Overall scores (converted from asymmetry percentages)
        eyebrowSymmetry: Math.max(0, 100 - regional.forehead.asymmetry_percentage),
        eyeSymmetry: Math.max(0, 100 - regional.eye.asymmetry_percentage),
        mouthSymmetry: Math.max(0, 100 - regional.smile.asymmetry_percentage),
        overallSymmetry: Math.max(0, 100 - (this.clinicalResults.composite_scores.facial_asymmetry_index * 100)),

        // House-Brackmann grade
        housebrackmannGrade: this.clinicalResults.overall_hb_grade
      };
    }

    // Priority: Use enhanced real-time calculations with precise MediaPipe landmarks
    if (this.landmarkStorage.size > 0) {
      console.log('Using enhanced real-time calculations with landmarks');
      const enhancedMetrics = this.calculateEnhancedSymmetryMetrics();
      return {
        ...enhancedMetrics,
        leftHorizontalDistance: enhancedMetrics.leftHorizontalDistance,
        rightHorizontalDistance: enhancedMetrics.rightHorizontalDistance,
        leftVerticalDistance: enhancedMetrics.leftVerticalDistance,
        rightVerticalDistance: enhancedMetrics.rightVerticalDistance
      };
    }

    // No fallback to mock data - throw error if no real data available
    console.error('❌ ERROR: No landmark data available for analysis');
    throw new Error('Analysis failed: No facial landmark data available. Please ensure face detection is working properly.');
  }

  /**
   * Calculate enhanced symmetry metrics using precise MediaPipe landmark measurements
   * Implements clinically accurate Bell's palsy assessment methodology
   */
  private calculateEnhancedSymmetryMetrics(): any {
    console.log('Calculating enhanced symmetry metrics using precise MediaPipe landmarks');

    // Get landmark data from storage
    const baseline = this.landmarkStorage.get('baseline') || [];
    const eyebrowRaise = this.landmarkStorage.get('eyebrowRaise') || [];
    const eyeClose = this.landmarkStorage.get('eyeClose') || [];
    const smile = this.landmarkStorage.get('smile') || [];

    // Validate required landmark data availability
    const dataQuality = this.validateLandmarkData(baseline, eyebrowRaise, eyeClose, smile);
    console.log('Landmark data quality assessment:', dataQuality);

    // Calculate enhanced measurements for each facial region
    const eyebrowAnalysis = this.calculateEnhancedEyebrowAnalysis(baseline, eyebrowRaise);
    const eyeAnalysis = this.calculateEnhancedEyeAnalysis(baseline, eyeClose);
    const mouthAnalysis = this.calculateEnhancedMouthAnalysis(baseline, smile);

    // Calculate mouth corner horizontal and vertical distances
    // Priority: Use captured peak frame measurements if available
    let mouthDistances;
    if (this.peakFrameTracking.peakMouthMovements) {
      console.log('Using captured peak frame mouth measurements');
      mouthDistances = {
        leftHorizontalDistance: this.peakFrameTracking.peakMouthMovements.leftHorizontalDistance,
        rightHorizontalDistance: this.peakFrameTracking.peakMouthMovements.rightHorizontalDistance,
        leftVerticalDistance: this.peakFrameTracking.peakMouthMovements.leftVerticalDistance,
        rightVerticalDistance: this.peakFrameTracking.peakMouthMovements.rightVerticalDistance,
        verticalAsymmetry: this.peakFrameTracking.peakMouthMovements.verticalAsymmetry,
        horizontalAsymmetry: this.peakFrameTracking.peakMouthMovements.horizontalAsymmetry
      };
      console.log('Peak frame mouth measurements:', mouthDistances);
    } else {
      console.log('No peak frame measurements available, calculating from stored landmarks');
      mouthDistances = this.calculateMouthCornerDistances(baseline, smile);
    }

    // Calculate overall symmetry scores
    const overallSymmetry = this.calculateOverallSymmetryScore(eyebrowAnalysis, eyeAnalysis, mouthAnalysis);

    // Detect synkinesis for enhanced analysis
    const synkinesisResults = this.detectBasicSynkinesis();
    const hasSynkinesis = synkinesisResults.length > 0;

    // Calculate House-Brackmann grade for real-time analysis
    const housebrackmannGrade = this.calculateRealTimeHouseBrackmannGrade(
      eyebrowAnalysis.asymmetryPercentage,
      eyeAnalysis.asymmetryPercentage,
      mouthAnalysis.asymmetryPercentage
    );

    console.log('Enhanced symmetry metrics - House-Brackmann grade:', housebrackmannGrade);
    console.log('Enhanced symmetry metrics - Asymmetry values:', {
      eyebrow: eyebrowAnalysis.asymmetryPercentage,
      eye: eyeAnalysis.asymmetryPercentage,
      mouth: mouthAnalysis.asymmetryPercentage
    });
    console.log('Enhanced symmetry metrics - Synkinesis detected:', hasSynkinesis);

    return {
      // Individual measurements for detailed clinical analysis
      leftEyebrowElevation: eyebrowAnalysis.leftElevation,
      rightEyebrowElevation: eyebrowAnalysis.rightElevation,
      eyebrowAsymmetry: eyebrowAnalysis.asymmetryPercentage,

      leftEyeClosure: eyeAnalysis.leftClosurePercentage,
      rightEyeClosure: eyeAnalysis.rightClosurePercentage,
      eyeAsymmetry: eyeAnalysis.asymmetryPercentage,

      leftMouthMovement: mouthAnalysis.leftCornerMovement,
      rightMouthMovement: mouthAnalysis.rightCornerMovement,
      mouthAsymmetry: mouthAnalysis.asymmetryPercentage,
      commissureDroop: mouthAnalysis.commissureDroop,

      // Added horizontal and vertical distances for mouth corners
      leftHorizontalDistance: mouthDistances.leftHorizontalDistance,
      rightHorizontalDistance: mouthDistances.rightHorizontalDistance,
      leftVerticalDistance: mouthDistances.leftVerticalDistance,
      rightVerticalDistance: mouthDistances.rightVerticalDistance,
      horizontalAsymmetry: mouthDistances.horizontalAsymmetry || 0,
      verticalAsymmetry: mouthDistances.verticalAsymmetry || 0,

      // Overall symmetry scores
      eyebrowSymmetry: Math.max(0, 100 - eyebrowAnalysis.asymmetryPercentage),
      eyeSymmetry: Math.max(0, 100 - eyeAnalysis.asymmetryPercentage),
      mouthSymmetry: Math.max(0, 100 - mouthAnalysis.asymmetryPercentage),
      overallSymmetry: overallSymmetry,

      // House-Brackmann grade for real-time analysis
      housebrackmannGrade: housebrackmannGrade,

      // Synkinesis detection data
      synkinesisDetected: hasSynkinesis,
      synkinesisResults: synkinesisResults,

      // Clinical documentation
      landmarkDocumentation: {
        eyebrowLandmarks: {
          left: [70, 63, 105, 66, 107],
          right: [300, 293, 334, 296, 336],
          reference: [33, 263]
        },
        eyeLandmarks: {
          left: [[159, 145], [158, 153], [160, 144]],
          right: [[386, 374], [385, 380], [387, 373]]
        },
        mouthLandmarks: {
          corners: [61, 291],
          central: [13, 14]
        }
      },
      dataQuality: dataQuality,
      calculationMethod: 'Enhanced MediaPipe Landmark Analysis v2.1'
    };
  }

  /**
   * Transform normalized MediaPipe coordinates (0-1) to a reference pixel scale
   * Uses a standard reference scale for consistent distance measurements
   */
  private transformToPixelScale(point: {x: number, y: number, z?: number}): {x: number, y: number, z: number} {
    // Use a standard reference scale (e.g., 640x480) for consistent measurements
    // This ensures distance calculations are in meaningful pixel units
    const REFERENCE_WIDTH = 640;
    const REFERENCE_HEIGHT = 480;

    return {
      x: point.x * REFERENCE_WIDTH,
      y: point.y * REFERENCE_HEIGHT,
      z: point.z || 0
    };
  }

  /**
   * Calculate perpendicular distance from a point to a line defined by two points
   * Uses the same 3D calculation method as AnalysisVisualizationService
   */
  private calculatePerpendicularDistance(point: {x: number, y: number, z?: number}, linePoint1: {x: number, y: number, z?: number}, linePoint2: {x: number, y: number, z?: number}): number {
    const x0 = point.x;
    const y0 = point.y;
    const z0 = point.z || 0;

    const x1 = linePoint1.x;
    const y1 = linePoint1.y;
    const z1 = linePoint1.z || 0;
    const x2 = linePoint2.x;
    const y2 = linePoint2.y;
    const z2 = linePoint2.z || 0;

    // Calculate 3D perpendicular distance from point to line
    // Vector from line point 1 to line point 2
    const lineVectorX = x2 - x1;
    const lineVectorY = y2 - y1;
    const lineVectorZ = z2 - z1;

    // Vector from line point 1 to the point
    const pointVectorX = x0 - x1;
    const pointVectorY = y0 - y1;
    const pointVectorZ = z0 - z1;

    // Cross product of line vector and point vector
    const crossX = lineVectorY * pointVectorZ - lineVectorZ * pointVectorY;
    const crossY = lineVectorZ * pointVectorX - lineVectorX * pointVectorZ;
    const crossZ = lineVectorX * pointVectorY - lineVectorY * pointVectorX;

    // Magnitude of cross product
    const crossMagnitude = Math.sqrt(crossX * crossX + crossY * crossY + crossZ * crossZ);

    // Magnitude of line vector
    const lineMagnitude = Math.sqrt(lineVectorX * lineVectorX + lineVectorY * lineVectorY + lineVectorZ * lineVectorZ);

    return lineMagnitude > 0 ? crossMagnitude / lineMagnitude : 0;
  }

  /**
   * Calculate horizontal and vertical distances for left and right mouth corners
   * Uses perpendicular distance calculations to vertical and horizontal reference lines
   * Based on AnalysisVisualizationService methodology
   */
  private calculateMouthCornerDistances(baseline: any[], smile: any[]): { leftHorizontalDistance: number; rightHorizontalDistance: number; leftVerticalDistance: number; rightVerticalDistance: number; verticalAsymmetry: number; horizontalAsymmetry: number } {
    if (baseline.length < 468 || smile.length < 468) {
      console.warn('Insufficient landmark data for mouth corner distance calculation');
      return {
        leftHorizontalDistance: 0,
        rightHorizontalDistance: 0,
        leftVerticalDistance: 0,
        rightVerticalDistance: 0,
        verticalAsymmetry: 0,
        horizontalAsymmetry: 0
      };
    }

    try {
      // Get key landmarks for reference lines
      const leftEyeInner = smile[133];   // Left eye inner corner
      const rightEyeInner = smile[362];  // Right eye inner corner
      const chinTip = smile[152];        // Chin tip (most stable reference point)
      const leftMouthCorner = smile[61]; // Left mouth corner
      const rightMouthCorner = smile[291]; // Right mouth corner

      // Debug: Log landmark coordinates to understand the coordinate system
      console.log('🔍 DEBUG: Landmark coordinates for distance calculation:');
      console.log(`  Left Eye Inner (133): x=${leftEyeInner?.x?.toFixed(6)}, y=${leftEyeInner?.y?.toFixed(6)}, z=${leftEyeInner?.z?.toFixed(6)}`);
      console.log(`  Right Eye Inner (362): x=${rightEyeInner?.x?.toFixed(6)}, y=${rightEyeInner?.y?.toFixed(6)}, z=${rightEyeInner?.z?.toFixed(6)}`);
      console.log(`  Chin Tip (152): x=${chinTip?.x?.toFixed(6)}, y=${chinTip?.y?.toFixed(6)}, z=${chinTip?.z?.toFixed(6)}`);
      console.log(`  Left Mouth Corner (61): x=${leftMouthCorner?.x?.toFixed(6)}, y=${leftMouthCorner?.y?.toFixed(6)}, z=${leftMouthCorner?.z?.toFixed(6)}`);
      console.log(`  Right Mouth Corner (291): x=${rightMouthCorner?.x?.toFixed(6)}, y=${rightMouthCorner?.y?.toFixed(6)}, z=${rightMouthCorner?.z?.toFixed(6)}`);

      if (!leftEyeInner || !rightEyeInner || !chinTip || !leftMouthCorner || !rightMouthCorner) {
        console.warn('Missing required landmarks for distance calculation');
        return {
          leftHorizontalDistance: 0,
          rightHorizontalDistance: 0,
          leftVerticalDistance: 0,
          rightVerticalDistance: 0,
          verticalAsymmetry: 0,
          horizontalAsymmetry: 0
        };
      }

      // Transform landmarks to pixel scale for meaningful distance calculations
      const leftEyeInnerPx = this.transformToPixelScale(leftEyeInner);
      const rightEyeInnerPx = this.transformToPixelScale(rightEyeInner);
      const chinTipPx = this.transformToPixelScale(chinTip);
      const leftMouthCornerPx = this.transformToPixelScale(leftMouthCorner);
      const rightMouthCornerPx = this.transformToPixelScale(rightMouthCorner);

      // Calculate eye midpoint for vertical reference line (in pixel coordinates)
      const eyeMidpointPx = {
        x: (leftEyeInnerPx.x + rightEyeInnerPx.x) / 2,
        y: (leftEyeInnerPx.y + rightEyeInnerPx.y) / 2,
        z: (leftEyeInnerPx.z + rightEyeInnerPx.z) / 2
      };

      console.log('🔍 DEBUG: Transformed pixel coordinates:');
      console.log(`  Eye Midpoint: x=${eyeMidpointPx.x.toFixed(2)}, y=${eyeMidpointPx.y.toFixed(2)}, z=${eyeMidpointPx.z.toFixed(2)}`);
      console.log(`  Chin Tip: x=${chinTipPx.x.toFixed(2)}, y=${chinTipPx.y.toFixed(2)}, z=${chinTipPx.z.toFixed(2)}`);
      console.log(`  Left Mouth: x=${leftMouthCornerPx.x.toFixed(2)}, y=${leftMouthCornerPx.y.toFixed(2)}, z=${leftMouthCornerPx.z.toFixed(2)}`);
      console.log(`  Right Mouth: x=${rightMouthCornerPx.x.toFixed(2)}, y=${rightMouthCornerPx.y.toFixed(2)}, z=${rightMouthCornerPx.z.toFixed(2)}`);

      // Calculate vertical distances from eye-to-chin midline
      const leftVerticalDistance = this.calculatePerpendicularDistance(leftMouthCornerPx, eyeMidpointPx, chinTipPx);
      const rightVerticalDistance = this.calculatePerpendicularDistance(rightMouthCornerPx, eyeMidpointPx, chinTipPx);

      // Calculate the angle of the inner eye line for horizontal reference (using pixel coordinates)
      const deltaY = rightEyeInnerPx.y - leftEyeInnerPx.y;
      const deltaX = rightEyeInnerPx.x - leftEyeInnerPx.x;
      const eyeAngle = Math.atan2(deltaY, deltaX);

      // Calculate tilted horizontal line endpoints at chin tip level (in pixel coordinates)
      const lineLength = Math.abs(rightEyeInnerPx.x - leftEyeInnerPx.x) * 1.5; // Use eye distance as reference
      const halfLength = lineLength / 2;
      const leftEndPointPx = {
        x: chinTipPx.x - halfLength * Math.cos(eyeAngle),
        y: chinTipPx.y - halfLength * Math.sin(eyeAngle),
        z: chinTipPx.z
      };
      const rightEndPointPx = {
        x: chinTipPx.x + halfLength * Math.cos(eyeAngle),
        y: chinTipPx.y + halfLength * Math.sin(eyeAngle),
        z: chinTipPx.z
      };

      console.log('🔍 DEBUG: Horizontal reference line endpoints:');
      console.log(`  Left End: x=${leftEndPointPx.x.toFixed(2)}, y=${leftEndPointPx.y.toFixed(2)}, z=${leftEndPointPx.z.toFixed(2)}`);
      console.log(`  Right End: x=${rightEndPointPx.x.toFixed(2)}, y=${rightEndPointPx.y.toFixed(2)}, z=${rightEndPointPx.z.toFixed(2)}`);
      console.log(`  Eye Angle: ${(eyeAngle * 180 / Math.PI).toFixed(2)} degrees`);
      console.log(`  Line Length: ${lineLength.toFixed(2)} pixels`);

      // Calculate horizontal distances from tilted horizontal line
      const leftHorizontalDistance = this.calculatePerpendicularDistance(leftMouthCornerPx, leftEndPointPx, rightEndPointPx);
      const rightHorizontalDistance = this.calculatePerpendicularDistance(rightMouthCornerPx, leftEndPointPx, rightEndPointPx);

      console.log('🎯 Enhanced mouth corner distances calculated:');
      console.log(`  Vertical distances - Left: ${leftVerticalDistance.toFixed(4)}px, Right: ${rightVerticalDistance.toFixed(4)}px`);
      console.log(`  Horizontal distances - Left: ${leftHorizontalDistance.toFixed(4)}px, Right: ${rightHorizontalDistance.toFixed(4)}px`);

      // Calculate asymmetries
      const maxVerticalDistance = Math.max(leftVerticalDistance, rightVerticalDistance);
      const maxHorizontalDistance = Math.max(leftHorizontalDistance, rightHorizontalDistance);

      const verticalAsymmetry = maxVerticalDistance > 0 ? Math.abs(leftVerticalDistance - rightVerticalDistance) / maxVerticalDistance * 100 : 0;
      const horizontalAsymmetry = maxHorizontalDistance > 0 ? Math.abs(leftHorizontalDistance - rightHorizontalDistance) / maxHorizontalDistance * 100 : 0;

      console.log(`  Asymmetries - Vertical: ${verticalAsymmetry.toFixed(1)}%, Horizontal: ${horizontalAsymmetry.toFixed(1)}%`);

      // Additional validation
      if (leftVerticalDistance === 0 && rightVerticalDistance === 0) {
        console.warn('⚠️ WARNING: All vertical distances are zero - check landmark availability and coordinate transformation');
      }
      if (leftHorizontalDistance === 0 && rightHorizontalDistance === 0) {
        console.warn('⚠️ WARNING: All horizontal distances are zero - check landmark availability and coordinate transformation');
      }

      return {
        leftHorizontalDistance,
        rightHorizontalDistance,
        leftVerticalDistance,
        rightVerticalDistance,
        verticalAsymmetry,
        horizontalAsymmetry
      };
    } catch (error) {
      console.error('Error calculating mouth corner distances:', error);
      return {
        leftHorizontalDistance: 0,
        rightHorizontalDistance: 0,
        leftVerticalDistance: 0,
        rightVerticalDistance: 0,
        verticalAsymmetry: 0,
        horizontalAsymmetry: 0
      };
    }
  }

  /**
   * Validate landmark data availability and quality for enhanced calculations
   */
  private validateLandmarkData(baseline: any[], eyebrowRaise: any[], eyeClose: any[], smile: any[]): any {
    const requiredLandmarks = {
      eyebrow: [70, 63, 105, 66, 107, 300, 293, 334, 296, 336, 33, 263], // Left + Right eyebrow + reference
      eye: [159, 145, 158, 153, 160, 144, 386, 374, 385, 380, 387, 373], // Left + Right eye pairs
      mouth: [61, 291, 13, 14] // Corner + central landmarks
    };

    const dataQuality = {
      baseline: { available: baseline.length >= 468, landmarkCount: baseline.length },
      eyebrowRaise: { available: eyebrowRaise.length >= 468, landmarkCount: eyebrowRaise.length },
      eyeClose: { available: eyeClose.length >= 468, landmarkCount: eyeClose.length },
      smile: { available: smile.length >= 468, landmarkCount: smile.length },
      requiredLandmarks: requiredLandmarks,
      overallQuality: 'excellent'
    };

    // Assess overall data quality
    const availableActions = [baseline, eyebrowRaise, eyeClose, smile].filter(action => action.length >= 468).length;
    if (availableActions === 4) {
      dataQuality.overallQuality = 'excellent';
    } else if (availableActions >= 3) {
      dataQuality.overallQuality = 'good';
    } else if (availableActions >= 2) {
      dataQuality.overallQuality = 'fair';
    } else {
      dataQuality.overallQuality = 'poor';
    }

    return dataQuality;
  }

  /**
   * Calculate enhanced eyebrow elevation analysis using five-point measurement system
   * Left eyebrow: landmarks 70, 63, 105, 66, 107 (comprehensive coverage)
   * Right eyebrow: landmarks 300, 293, 334, 296, 336 (mirror positions)
   * Reference: horizontal line connecting landmarks 33-263 (outer canthi)
   */
  private calculateEnhancedEyebrowAnalysis(baseline: any[], eyebrowRaise: any[]): any {
    console.log('Calculating enhanced eyebrow analysis using landmarks 70,63,105,66,107 (left) and 300,293,334,296,336 (right)');

    if (baseline.length < 468 || eyebrowRaise.length < 468) {
      console.warn('Insufficient landmark data for eyebrow analysis');
      return {
        leftElevation: 0,
        rightElevation: 0,
        asymmetryPercentage: 0,
        dataQuality: 'insufficient',
        landmarksUsed: { left: [70, 63, 105, 66, 107], right: [300, 293, 334, 296, 336], reference: [33, 263] }
      };
    }

    try {
      // Get reference line (outer canthi) for baseline normalization
      const leftCanthus = baseline[33];
      const rightCanthus = baseline[263];
      const referenceY = (leftCanthus.y + rightCanthus.y) / 2;

      // Define specific eyebrow landmarks as requested
      const leftEyebrowLandmarks = [70, 63, 105, 66, 107];
      const rightEyebrowLandmarks = [300, 293, 334, 296, 336];

      // Calculate baseline eyebrow positions
      const baselineLeftEyebrow = leftEyebrowLandmarks.map(idx => baseline[idx]);
      const baselineRightEyebrow = rightEyebrowLandmarks.map(idx => baseline[idx]);

      // Calculate raised eyebrow positions
      const raisedLeftEyebrow = leftEyebrowLandmarks.map(idx => eyebrowRaise[idx]);
      const raisedRightEyebrow = rightEyebrowLandmarks.map(idx => eyebrowRaise[idx]);

      // Validate all landmarks are available
      const missingLeftBaseline = baselineLeftEyebrow.some(point => !point);
      const missingRightBaseline = baselineRightEyebrow.some(point => !point);
      const missingLeftRaised = raisedLeftEyebrow.some(point => !point);
      const missingRightRaised = raisedRightEyebrow.some(point => !point);

      if (missingLeftBaseline || missingRightBaseline || missingLeftRaised || missingRightRaised) {
        console.warn('Missing eyebrow landmark points:', {
          missingLeftBaseline, missingRightBaseline, missingLeftRaised, missingRightRaised
        });
        return {
          leftElevation: 0,
          rightElevation: 0,
          asymmetryPercentage: 0,
          dataQuality: 'incomplete',
          landmarksUsed: { left: leftEyebrowLandmarks, right: rightEyebrowLandmarks, reference: [33, 263] }
        };
      }

      // Calculate face size for normalization (distance between outer canthi)
      const faceWidth = Math.sqrt(
        Math.pow(baseline[454].x - baseline[234].x, 2) +
        Math.pow(baseline[454].y - baseline[234].y, 2)
      );

      // Calculate mean elevation for left eyebrow (normalized by face size)
      const leftElevations = baselineLeftEyebrow.map((baselinePoint, i) => {
        const raisedPoint = raisedLeftEyebrow[i];
        const rawElevation = Math.abs(baselinePoint.y - raisedPoint.y);
        return (rawElevation / faceWidth) * 100; // Normalize by face size and convert to percentage
      });
      const leftMeanElevation = leftElevations.reduce((sum, val) => sum + val, 0) / leftElevations.length;

      // Calculate mean elevation for right eyebrow (normalized by face size)
      const rightElevations = baselineRightEyebrow.map((baselinePoint, i) => {
        const raisedPoint = raisedRightEyebrow[i];
        const rawElevation = Math.abs(baselinePoint.y - raisedPoint.y);
        return (rawElevation / faceWidth) * 100; // Normalize by face size and convert to percentage
      });
      const rightMeanElevation = rightElevations.reduce((sum, val) => sum + val, 0) / rightElevations.length;

      // Calculate asymmetry percentage with baseline correction for natural asymmetry
      const baselineAsymmetryCorrection = 2.0; // Normal faces have ~2% natural asymmetry
      const maxElevation = Math.max(leftMeanElevation, rightMeanElevation);
      const rawAsymmetryPercentage = maxElevation > 0 ?
        (Math.abs(leftMeanElevation - rightMeanElevation) / maxElevation) * 100 : 0;

      // Apply baseline correction - subtract natural asymmetry
      const asymmetryPercentage = Math.max(0, rawAsymmetryPercentage - baselineAsymmetryCorrection);

      console.log(`Eyebrow analysis: Left=${leftMeanElevation.toFixed(2)}mm, Right=${rightMeanElevation.toFixed(2)}mm, Asymmetry=${asymmetryPercentage.toFixed(1)}%`);

      return {
        leftElevation: leftMeanElevation,
        rightElevation: rightMeanElevation,
        asymmetryPercentage: asymmetryPercentage,
        dataQuality: 'excellent',
        landmarksUsed: { left: leftEyebrowLandmarks, right: rightEyebrowLandmarks, reference: [33, 263] },
        individualMeasurements: {
          left: leftElevations,
          right: rightElevations
        }
      };
    } catch (error) {
      console.error('Error in eyebrow analysis calculation:', error);
      return {
        leftElevation: 0,
        rightElevation: 0,
        asymmetryPercentage: 0,
        dataQuality: 'error',
        error: String(error)
      };
    }
  }

  /**
   * Calculate enhanced eye closure analysis using three-point measurement system
   * Left eye: pairs 159-145 (lateral), 158-153 (central), 160-144 (medial)
   * Right eye: pairs 386-374 (lateral), 385-380 (central), 387-373 (medial)
   */
  private calculateEnhancedEyeAnalysis(baseline: any[], eyeClose: any[]): any {
    console.log('Calculating enhanced eye closure analysis using landmarks 159-145,158-153,160-144 (left) and 386-374,385-380,387-373 (right)');

    if (baseline.length < 468 || eyeClose.length < 468) {
      console.warn('Insufficient landmark data for eye closure analysis');
      return {
        leftClosurePercentage: 0,
        rightClosurePercentage: 0,
        asymmetryPercentage: 0,
        dataQuality: 'insufficient',
        landmarksUsed: {
          left: [[159, 145], [158, 153], [160, 144]],
          right: [[386, 374], [385, 380], [387, 373]]
        }
      };
    }

    try {
      // Define eye measurement pairs as specified
      const leftEyePairs = [[159, 145], [158, 153], [160, 144]];
      const rightEyePairs = [[386, 374], [385, 380], [387, 373]];

      // Calculate baseline eye opening distances
      const baselineLeftDistances = leftEyePairs.map(([upper, lower]) => {
        const upperPoint = baseline[upper];
        const lowerPoint = baseline[lower];
        return Math.sqrt(Math.pow(upperPoint.x - lowerPoint.x, 2) + Math.pow(upperPoint.y - lowerPoint.y, 2));
      });

      const baselineRightDistances = rightEyePairs.map(([upper, lower]) => {
        const upperPoint = baseline[upper];
        const lowerPoint = baseline[lower];
        return Math.sqrt(Math.pow(upperPoint.x - lowerPoint.x, 2) + Math.pow(upperPoint.y - lowerPoint.y, 2));
      });

      // Calculate eye closure distances
      const closureLeftDistances = leftEyePairs.map(([upper, lower]) => {
        const upperPoint = eyeClose[upper];
        const lowerPoint = eyeClose[lower];
        return Math.sqrt(Math.pow(upperPoint.x - lowerPoint.x, 2) + Math.pow(upperPoint.y - lowerPoint.y, 2));
      });

      const closureRightDistances = rightEyePairs.map(([upper, lower]) => {
        const upperPoint = eyeClose[upper];
        const lowerPoint = eyeClose[lower];
        return Math.sqrt(Math.pow(upperPoint.x - lowerPoint.x, 2) + Math.pow(upperPoint.y - lowerPoint.y, 2));
      });

      // Calculate mean distances
      const leftBaselineMean = baselineLeftDistances.reduce((sum, val) => sum + val, 0) / baselineLeftDistances.length;
      const rightBaselineMean = baselineRightDistances.reduce((sum, val) => sum + val, 0) / baselineRightDistances.length;
      const leftClosureMean = closureLeftDistances.reduce((sum, val) => sum + val, 0) / closureLeftDistances.length;
      const rightClosureMean = closureRightDistances.reduce((sum, val) => sum + val, 0) / closureRightDistances.length;

      // Calculate closure percentages
      const leftClosurePercentage = leftBaselineMean > 0 ?
        ((leftBaselineMean - leftClosureMean) / leftBaselineMean) * 100 : 0;
      const rightClosurePercentage = rightBaselineMean > 0 ?
        ((rightBaselineMean - rightClosureMean) / rightBaselineMean) * 100 : 0;

      // Calculate asymmetry with baseline correction for natural asymmetry
      const baselineAsymmetryCorrection = 3.0; // Normal faces have ~3% natural eye asymmetry
      const rawAsymmetryPercentage = Math.abs(leftClosurePercentage - rightClosurePercentage);
      const asymmetryPercentage = Math.max(0, rawAsymmetryPercentage - baselineAsymmetryCorrection);

      // Detect lagophthalmos (incomplete closure)
      const lagophthalmos = {
        left: leftClosurePercentage < 80, // Less than 80% closure indicates incomplete closure
        right: rightClosurePercentage < 80,
        severity: Math.min(leftClosurePercentage, rightClosurePercentage) < 60 ? 'severe' : 'mild'
      };

      console.log(`Eye closure analysis: Left=${leftClosurePercentage.toFixed(1)}%, Right=${rightClosurePercentage.toFixed(1)}%, Asymmetry=${asymmetryPercentage.toFixed(1)}%`);

      return {
        leftClosurePercentage: leftClosurePercentage,
        rightClosurePercentage: rightClosurePercentage,
        asymmetryPercentage: asymmetryPercentage,
        lagophthalmos: lagophthalmos,
        dataQuality: 'excellent',
        landmarksUsed: {
          left: [[159, 145], [158, 153], [160, 144]],
          right: [[386, 374], [385, 380], [387, 373]]
        },
        individualMeasurements: {
          leftBaseline: baselineLeftDistances,
          rightBaseline: baselineRightDistances,
          leftClosure: closureLeftDistances,
          rightClosure: closureRightDistances
        }
      };
    } catch (error) {
      console.error('Error in eye closure analysis calculation:', error);
      return {
        leftClosurePercentage: 0,
        rightClosurePercentage: 0,
        asymmetryPercentage: 0,
        dataQuality: 'error',
        error: String(error)
      };
    }
  }

  /**
   * ENHANCED: Landmark-Relative Mouth Analysis
   * Uses the new landmark-relative displacement analysis for improved accuracy
   */
  private calculateEnhancedMouthAnalysis(baseline: any[], smile: any[]): any {
    console.log('🔬 ENHANCED LANDMARK-RELATIVE MOUTH ANALYSIS');

    if (baseline.length < 468 || smile.length < 468) {
      console.warn('Insufficient landmark data for mouth movement analysis');
      return {
        leftMovement: 0,
        rightMovement: 0,
        asymmetryIndex: 0,
        severity: 'Error',
        affectedSide: 'Unknown',
        dataQuality: 'insufficient',
        landmarksUsed: { corners: [61, 291], normalization: [33, 263] }
      };
    }

    try {
      // Use the enhanced landmark-relative analysis function
      const clinicalResult = this.analyze_smile_movement(baseline, smile);

      // Calculate enhanced perpendicular distances using AnalysisVisualizationService methodology
      const enhancedDistances = this.calculateMouthCornerDistances(baseline, smile);

      console.log('📊 ENHANCED CLINICAL ANALYSIS RESULTS:');
      console.log(`   Left Movement: ${clinicalResult.left_movement.toFixed(6)}`);
      console.log(`   Right Movement: ${clinicalResult.right_movement.toFixed(6)}`);
      console.log(`   Asymmetry Index: ${clinicalResult.asymmetry_index.toFixed(6)}`);
      console.log(`   Severity: ${clinicalResult.severity}`);
      console.log(`   Affected Side: ${clinicalResult.affected_side}`);

      console.log('📐 ENHANCED PERPENDICULAR DISTANCE ANALYSIS:');
      console.log(`   Vertical Distances - Left: ${enhancedDistances.leftVerticalDistance.toFixed(6)}, Right: ${enhancedDistances.rightVerticalDistance.toFixed(6)}`);
      console.log(`   Horizontal Distances - Left: ${enhancedDistances.leftHorizontalDistance.toFixed(6)}, Right: ${enhancedDistances.rightHorizontalDistance.toFixed(6)}`);

      // Calculate asymmetry for enhanced distances
      const verticalAsymmetry = Math.abs(enhancedDistances.leftVerticalDistance - enhancedDistances.rightVerticalDistance);
      const horizontalAsymmetry = Math.abs(enhancedDistances.leftHorizontalDistance - enhancedDistances.rightHorizontalDistance);
      console.log(`   Distance Asymmetries - Vertical: ${verticalAsymmetry.toFixed(6)}, Horizontal: ${horizontalAsymmetry.toFixed(6)}`);

      // Enhanced displacement data logging (vertical midline method)
      if (clinicalResult.horizontal_displacement) {
        console.log('📐 HORIZONTAL DISPLACEMENT (Vertical Midline Method):');
        console.log(`   Left: ${clinicalResult.horizontal_displacement.left.toFixed(6)} (displacement from midline)`);
        console.log(`   Right: ${clinicalResult.horizontal_displacement.right.toFixed(6)} (displacement from midline)`);
        console.log(`   Asymmetry: ${clinicalResult.horizontal_displacement.asymmetry.toFixed(6)}`);
        console.log(`   Method: ${clinicalResult.horizontal_displacement.method}`);
      }

      // 3D pose-invariant analysis logging
      if (clinicalResult.midline_analysis) {
        console.log('📐 3D POSE-INVARIANT ANALYSIS:');
        console.log(`   Face width (IPD): ${clinicalResult.midline_analysis.face_width_ipd.toFixed(6)}`);
        console.log(`   Analysis type: ${clinicalResult.midline_analysis.analysis_type}`);
        console.log(`   Landmark coverage: ${(clinicalResult.midline_analysis.landmark_coverage * 100).toFixed(1)}% (${clinicalResult.midline_analysis.coverage_quality})`);
      }

      // Movement measurements logging (3D normalized movements)
      if (clinicalResult.distance_measurements) {
        console.log('📏 3D MOVEMENT MEASUREMENTS:');
        console.log(`   Left side: Raw 3D movement=${clinicalResult.distance_measurements.left_corner.raw_3d_movement.toFixed(6)}, Normalized=${clinicalResult.distance_measurements.left_corner.normalized_movement.toFixed(6)}`);
        console.log(`   Right side: Raw 3D movement=${clinicalResult.distance_measurements.right_corner.raw_3d_movement.toFixed(6)}, Normalized=${clinicalResult.distance_measurements.right_corner.normalized_movement.toFixed(6)}`);
      }

      // Quality assessment logging (3D landmark validation)
      if (clinicalResult.measurement_quality) {
        console.log('📊 3D LANDMARK QUALITY ASSESSMENT:');
        console.log(`   Landmark Coverage: ${clinicalResult.measurement_quality.landmark_coverage_percent.toFixed(1)}%`);
        console.log(`   Coverage Quality: ${clinicalResult.measurement_quality.coverage_quality}`);
        console.log(`   Valid Landmarks: ${clinicalResult.measurement_quality.valid_landmarks}/${clinicalResult.measurement_quality.total_landmarks}`);
        console.log(`   Overall Quality: ${clinicalResult.measurement_quality.overall_quality}`);
      }

      // Use raw horizontal distances (no scaling/conversion)
      const leftMovementRaw = clinicalResult.left_movement;
      const rightMovementRaw = clinicalResult.right_movement;
      const asymmetryPercentage = clinicalResult.asymmetry_index * 100;

      console.log('📏 RAW HORIZONTAL DISTANCES (No Scaling):');
      console.log(`   Left Distance: ${leftMovementRaw.toFixed(6)} pixels`);
      console.log(`   Right Distance: ${rightMovementRaw.toFixed(6)} pixels`);
      console.log(`   Asymmetry: ${asymmetryPercentage.toFixed(1)}%`);

      // Validation for raw pixel distances
      if (leftMovementRaw < 0.001 || rightMovementRaw < 0.001) {
        console.warn('⚠️ WARNING: Very low distance values detected');
        console.warn(`   This may indicate: weak smile, facial weakness, or measurement issues`);
        console.warn(`   Coverage quality: ${clinicalResult.measurement_quality?.coverage_quality || 'unknown'}`);
      }

      if (leftMovementRaw > 0.1 || rightMovementRaw > 0.1) {
        console.warn('⚠️ WARNING: High distance values detected');
        console.warn(`   This may indicate: excessive movement, synkinesis, or measurement artifacts`);
        console.warn(`   Valid landmarks: ${clinicalResult.measurement_quality?.valid_landmarks || 'unknown'}/${clinicalResult.measurement_quality?.total_landmarks || 'unknown'}`);
      }

      // Calculate overall score based on clinical severity
      let overallScore = 100;
      if (clinicalResult.severity === 'Complete Paralysis') {
        overallScore = 0;
      } else if (clinicalResult.severity === 'Severe asymmetry') {
        overallScore = 25;
      } else if (clinicalResult.severity === 'Moderate asymmetry') {
        overallScore = 50;
      } else if (clinicalResult.severity === 'Mild asymmetry') {
        overallScore = 75;
      } else if (clinicalResult.severity === 'Symmetrical (Normal)') {
        overallScore = 95;
      }

      console.log(`📈 Overall Mouth Score: ${overallScore}%`);

      return {
        // Main results using raw horizontal distances
        leftMovement: leftMovementRaw,
        rightMovement: rightMovementRaw,
        asymmetryIndex: clinicalResult.asymmetry_index,
        asymmetryPercentage: asymmetryPercentage,
        severity: clinicalResult.severity,
        affectedSide: clinicalResult.affected_side,
        overallScore: overallScore,
        dataQuality: 'excellent',
        landmarksUsed: { corners: [61, 291] },

        // Legacy compatibility fields for existing UI
        leftCornerMovement: leftMovementRaw,
        rightCornerMovement: rightMovementRaw,

        // Raw horizontal displacement components
        horizontalDisplacement: clinicalResult.horizontal_displacement ? {
          left: clinicalResult.horizontal_displacement.left,
          right: clinicalResult.horizontal_displacement.right,
          asymmetry: clinicalResult.horizontal_displacement.asymmetry,
          method: clinicalResult.horizontal_displacement.method || 'horizontal_only'
        } : null,

        // Peak frame midline analysis data
        midlineAnalysis: clinicalResult.midline_analysis ? {
          faceWidthIPD: clinicalResult.midline_analysis.face_width_ipd,
          analysisType: clinicalResult.midline_analysis.analysis_type,
          landmarkCoverage: clinicalResult.midline_analysis.landmark_coverage,
          coverageQuality: clinicalResult.midline_analysis.coverage_quality
        } : null,

        // Distance measurements (raw horizontal distances only)
        distanceMeasurements: clinicalResult.distance_measurements ? {
          leftCorner: {
            raw3DMovement: clinicalResult.distance_measurements.left_corner.raw_3d_movement,
            normalizedMovement: clinicalResult.distance_measurements.left_corner.normalized_movement
          },
          rightCorner: {
            raw3DMovement: clinicalResult.distance_measurements.right_corner.raw_3d_movement,
            normalizedMovement: clinicalResult.distance_measurements.right_corner.normalized_movement
          }
        } : null,

        // Quality metrics (peak frame symmetry method)
        measurementQuality: clinicalResult.measurement_quality ? {
          landmarkCoveragePercent: clinicalResult.measurement_quality.landmark_coverage_percent,
          coverageQuality: clinicalResult.measurement_quality.coverage_quality,
          validLandmarks: clinicalResult.measurement_quality.valid_landmarks,
          totalLandmarks: clinicalResult.measurement_quality.total_landmarks,
          overallQuality: clinicalResult.measurement_quality.overall_quality,
          qualityGrade: this.getQualityGrade(clinicalResult.measurement_quality)
        } : null,

        // Enhanced perpendicular distance measurements (AnalysisVisualizationService methodology)
        enhancedDistances: {
          leftVerticalDistance: enhancedDistances.leftVerticalDistance,
          rightVerticalDistance: enhancedDistances.rightVerticalDistance,
          leftHorizontalDistance: enhancedDistances.leftHorizontalDistance,
          rightHorizontalDistance: enhancedDistances.rightHorizontalDistance,
          verticalAsymmetry: verticalAsymmetry,
          horizontalAsymmetry: horizontalAsymmetry,
          method: 'perpendicular_distance_to_reference_lines',
          referenceLines: {
            vertical: 'eye_midpoint_to_chin_tip',
            horizontal: 'tilted_line_at_chin_level_parallel_to_eye_line'
          }
        },

        // Clinical analysis details (anatomical midline method)
        clinicalAnalysis: {
          left_movement_raw: clinicalResult.left_movement,
          right_movement_raw: clinicalResult.right_movement,
          asymmetry_index: clinicalResult.asymmetry_index,
          severity_classification: clinicalResult.severity,
          affected_side: clinicalResult.affected_side,
          analysis_method: clinicalResult.analysis_method || 'anatomical_midline_analysis',
          landmarks_used: clinicalResult.landmarks_used,
          scaling_factor: 'none'
        }
      };
    } catch (error) {
      console.error('Error in enhanced mouth movement analysis calculation:', error);
      return {
        leftMovement: 0,
        rightMovement: 0,
        asymmetryIndex: 0,
        severity: 'Error',
        affectedSide: 'Unknown',
        dataQuality: 'error',
        landmarksUsed: { corners: [61, 291], normalization: [33, 263] },
        error: String(error),
        // Legacy compatibility fields
        leftCornerMovement: 0,
        rightCornerMovement: 0,
        asymmetryPercentage: 0
      };
    }
  }

  /**
   * Calculate overall symmetry score based on regional analysis results
   * Weighted scoring system based on clinical importance
   */
  private calculateOverallSymmetryScore(eyebrowAnalysis: any, eyeAnalysis: any, mouthAnalysis: any): number {
    console.log('Calculating overall symmetry score from regional analyses');

    try {
      // FIXED: Standardized weights consistent across entire application
      const weights = {
        eyebrow: 0.30,  // 30% - Important for forehead function
        eye: 0.40,      // 40% - Critical for eye protection (most important)
        mouth: 0.30     // 30% - Important for facial expression and speech
      };

      // Calculate individual symmetry scores (100 - asymmetry percentage)
      const eyebrowSymmetry = Math.max(0, 100 - eyebrowAnalysis.asymmetryPercentage);
      const eyeSymmetry = Math.max(0, 100 - eyeAnalysis.asymmetryPercentage);
      const mouthSymmetry = Math.max(0, 100 - mouthAnalysis.asymmetryPercentage);

      // Apply penalties for specific clinical findings
      let eyePenalty = 0;
      if (eyeAnalysis.lagophthalmos && (eyeAnalysis.lagophthalmos.left || eyeAnalysis.lagophthalmos.right)) {
        eyePenalty = eyeAnalysis.lagophthalmos.severity === 'severe' ? 20 : 10;
        console.log(`Applied lagophthalmos penalty: ${eyePenalty} points`);
      }

      let mouthPenalty = 0;
      if (mouthAnalysis.commissureDroop > 5) {
        mouthPenalty = Math.min(15, mouthAnalysis.commissureDroop * 2); // Max 15 point penalty
        console.log(`Applied commissure droop penalty: ${mouthPenalty} points`);
      }

      // Calculate weighted overall score
      const weightedScore = (
        (eyebrowSymmetry * weights.eyebrow) +
        (Math.max(0, eyeSymmetry - eyePenalty) * weights.eye) +
        (Math.max(0, mouthSymmetry - mouthPenalty) * weights.mouth)
      );

      // Apply data quality adjustment
      const dataQualityMultiplier = this.getDataQualityMultiplier(eyebrowAnalysis, eyeAnalysis, mouthAnalysis);
      const finalScore = Math.max(0, Math.min(100, weightedScore * dataQualityMultiplier));

      console.log(`Overall symmetry calculation: Eyebrow=${eyebrowSymmetry.toFixed(1)}%, Eye=${eyeSymmetry.toFixed(1)}%, Mouth=${mouthSymmetry.toFixed(1)}%, Final=${finalScore.toFixed(1)}%`);

      return finalScore;
    } catch (error) {
      console.error('Error calculating overall symmetry score:', error);
      throw new Error('Failed to calculate overall symmetry score: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  /**
   * Get data quality multiplier based on analysis quality
   */
  private getDataQualityMultiplier(eyebrowAnalysis: any, eyeAnalysis: any, mouthAnalysis: any): number {
    const qualityScores = [eyebrowAnalysis.dataQuality, eyeAnalysis.dataQuality, mouthAnalysis.dataQuality];
    const excellentCount = qualityScores.filter(q => q === 'excellent').length;
    const goodCount = qualityScores.filter(q => q === 'good').length;
    const errorCount = qualityScores.filter(q => q === 'error' || q === 'insufficient').length;

    if (excellentCount === 3) return 1.0;      // Perfect data
    if (excellentCount >= 2) return 0.95;     // Very good data
    if (goodCount >= 2) return 0.90;          // Good data
    if (errorCount === 0) return 0.85;        // Fair data
    if (errorCount === 1) return 0.75;        // Poor data with some errors
    return 0.60; // Very poor data quality
  }

  /**
   * Calculate House-Brackmann grade for real-time analysis
   * Uses clinical thresholds based on facial asymmetry index AND synkinesis detection
   * MEDICAL ACCURACY: Synkinesis presence indicates at least Grade III per clinical standards
   */
  private calculateRealTimeHouseBrackmannGrade(eyebrowAsymmetry: number, eyeAsymmetry: number, mouthAsymmetry: number): number {
    console.log('Calculating real-time House-Brackmann grade with synkinesis integration');

    try {
      // Calculate composite facial asymmetry index (0-100 scale)
      // Weighted average based on clinical importance
      const facialAsymmetryIndex = (
        (eyebrowAsymmetry * 0.30) +  // 30% weight for eyebrow
        (eyeAsymmetry * 0.40) +      // 40% weight for eye (most critical)
        (mouthAsymmetry * 0.30)      // 30% weight for mouth
      );

      // Detect synkinesis for clinical accuracy
      const synkinesisResults = this.detectBasicSynkinesis();
      const hasSynkinesis = synkinesisResults.length > 0;

      // Calculate synkinesis burden score
      let synkinesisBurdenScore = 0;
      if (hasSynkinesis) {
        synkinesisBurdenScore = synkinesisResults.reduce((total, synkinesis) => {
          // Extract percentage from measurement string (e.g., "5.2% unintended movement")
          const percentageMatch = synkinesis.measurement.match(/(\d+\.?\d*)%/);
          const percentage = percentageMatch ? parseFloat(percentageMatch[1]) : 0;
          return total + percentage;
        }, 0) / synkinesisResults.length; // Average synkinesis percentage
      }

      console.log(`Synkinesis detected: ${hasSynkinesis}, burden score: ${synkinesisBurdenScore.toFixed(1)}%`);

      // Base House-Brackmann grade from asymmetry alone
      let baseHbGrade: number;
      if (facialAsymmetryIndex < 5) {
        baseHbGrade = 1; // Normal facial function
      } else if (facialAsymmetryIndex < 15) {
        baseHbGrade = 2; // Mild dysfunction
      } else if (facialAsymmetryIndex < 30) {
        baseHbGrade = 3; // Moderate dysfunction
      } else if (facialAsymmetryIndex < 50) {
        baseHbGrade = 4; // Moderately severe dysfunction
      } else if (facialAsymmetryIndex < 75) {
        baseHbGrade = 5; // Severe dysfunction
      } else {
        baseHbGrade = 6; // Total paralysis
      }

      // CLINICAL ACCURACY: Apply synkinesis adjustment per medical standards
      let finalHbGrade = baseHbGrade;

      if (hasSynkinesis) {
        // Only apply synkinesis adjustment if there's meaningful facial asymmetry (>3%)
        // This prevents false positives in normal subjects from affecting the grade
        if (facialAsymmetryIndex > 3) {
          // Synkinesis presence with asymmetry indicates at least Grade III (moderate dysfunction)
          if (synkinesisBurdenScore > 35) { // Severe synkinesis
            finalHbGrade = Math.max(finalHbGrade, 4); // At least Grade IV
          } else if (synkinesisBurdenScore > 25) { // Moderate synkinesis
            finalHbGrade = Math.max(finalHbGrade, 3); // At least Grade III
          } else if (synkinesisBurdenScore > 15) { // Mild synkinesis
            finalHbGrade = Math.max(finalHbGrade, 3); // At least Grade III (clinical standard)
          }
          console.log(`Synkinesis adjustment applied: Base grade ${baseHbGrade} → Final grade ${finalHbGrade} (asymmetry: ${facialAsymmetryIndex.toFixed(1)}%, synkinesis: ${synkinesisBurdenScore.toFixed(1)}%)`);
        } else {
          console.log(`Synkinesis detected but asymmetry too low (${facialAsymmetryIndex.toFixed(1)}%) - no grade adjustment applied`);
        }
      }

      console.log(`House-Brackmann calculation: Asymmetry=${facialAsymmetryIndex.toFixed(1)}%, Synkinesis=${synkinesisBurdenScore.toFixed(1)}%, Final Grade=${finalHbGrade}`);

      return finalHbGrade;
    } catch (error) {
      console.error('Error calculating House-Brackmann grade:', error);
      throw new Error('Failed to calculate House-Brackmann grade: ' + (error instanceof Error ? error.message : String(error)));
    }
  }

  private detectAsymmetries(): any[] {
    const asymmetries: any[] = [];

    // Use clinical analysis if available
    if (this.clinicalResults) {
      const regional = this.clinicalResults.regional_analysis;

      // Eyebrow asymmetries
      if (regional.forehead.asymmetry_percentage > 5) {
        asymmetries.push({
          feature: `${regional.forehead.affected_side === 'left' ? 'Left' : 'Right'} Eyebrow`,
          severity: this.getSeverityFromPercentage(regional.forehead.asymmetry_percentage),
          measurement: `${regional.forehead.asymmetry_percentage.toFixed(1)}% asymmetry`,
          recommendation: this.getRecommendationForSeverity(regional.forehead.severity_grade)
        });
      }

      // Eye asymmetries
      if (regional.eye.asymmetry_percentage > 5) {
        asymmetries.push({
          feature: `${regional.eye.affected_side === 'left' ? 'Left' : 'Right'} Eye Closure`,
          severity: this.getSeverityFromPercentage(regional.eye.asymmetry_percentage),
          measurement: `${regional.eye.asymmetry_percentage.toFixed(1)}% asymmetry`,
          recommendation: this.getRecommendationForSeverity(regional.eye.severity_grade)
        });

        // Add lagophthalmos if present
        if (regional.eye.lagophthalmos_present) {
          asymmetries.push({
            feature: 'Incomplete Eye Closure',
            severity: 'Moderate',
            measurement: 'Lagophthalmos detected',
            recommendation: 'Eye protection measures recommended'
          });
        }
      }

      // Mouth asymmetries
      if (regional.smile.asymmetry_percentage > 5) {
        asymmetries.push({
          feature: `${regional.smile.affected_side === 'left' ? 'Left' : 'Right'} Mouth Corner`,
          severity: this.getSeverityFromPercentage(regional.smile.asymmetry_percentage),
          measurement: `${regional.smile.asymmetry_percentage.toFixed(1)}% asymmetry`,
          recommendation: this.getRecommendationForSeverity(regional.smile.severity_grade)
        });

        // Add commissure droop if significant
        if (regional.smile.commissure_droop && regional.smile.commissure_droop > 2) {
          asymmetries.push({
            feature: 'Commissure Droop',
            severity: regional.smile.commissure_droop > 5 ? 'Moderate' : 'Mild',
            measurement: `${regional.smile.commissure_droop.toFixed(1)}% droop`,
            recommendation: 'Consider facial rehabilitation therapy'
          });
        }
      }

      // Synkinesis detection
      if (this.clinicalResults.synkinesis_analysis.eye_mouth_synkinesis.present) {
        asymmetries.push({
          feature: 'Eye-Mouth Synkinesis',
          severity: this.clinicalResults.synkinesis_analysis.eye_mouth_synkinesis.severity,
          measurement: `${this.clinicalResults.synkinesis_analysis.eye_mouth_synkinesis.percentage.toFixed(1)}% unintended movement`,
          recommendation: 'Synkinesis management program recommended'
        });
      }

      if (this.clinicalResults.synkinesis_analysis.mouth_eye_synkinesis.present) {
        asymmetries.push({
          feature: 'Mouth-Eye Synkinesis',
          severity: this.clinicalResults.synkinesis_analysis.mouth_eye_synkinesis.severity,
          measurement: `${this.clinicalResults.synkinesis_analysis.mouth_eye_synkinesis.percentage.toFixed(1)}% unintended movement`,
          recommendation: 'Consider botulinum toxin evaluation'
        });
      }
    } else if (this.landmarkStorage.size > 0) {
      // Fallback to real-time calculations
      const metrics = this.calculateRealTimeSymmetryMetrics();

      // Adjusted thresholds based on normal baseline data analysis
      // Eyebrow: Most reliable measurement - use stricter threshold
      if (metrics.eyebrowAsymmetry > 8) {
        asymmetries.push({
          feature: 'Eyebrow Elevation',
          severity: this.getSeverityFromPercentage(metrics.eyebrowAsymmetry),
          measurement: `${metrics.eyebrowAsymmetry.toFixed(1)}% asymmetry`,
          recommendation: this.getRecommendationForSeverity(this.getSeverityFromPercentage(metrics.eyebrowAsymmetry))
        });
      }

      // Eye: Very reliable measurement - use stricter threshold
      if (metrics.eyeAsymmetry > 6) {
        asymmetries.push({
          feature: 'Eye Closure',
          severity: this.getSeverityFromPercentage(metrics.eyeAsymmetry),
          measurement: `${metrics.eyeAsymmetry.toFixed(1)}% asymmetry`,
          recommendation: this.getRecommendationForSeverity(this.getSeverityFromPercentage(metrics.eyeAsymmetry))
        });
      }

      // Mouth: More variable in normal individuals - use higher threshold
      if (metrics.mouthAsymmetry > 25) {
        asymmetries.push({
          feature: 'Mouth Movement',
          severity: this.getSeverityFromPercentage(metrics.mouthAsymmetry),
          measurement: `${metrics.mouthAsymmetry.toFixed(1)}% asymmetry`,
          recommendation: this.getRecommendationForSeverity(this.getSeverityFromPercentage(metrics.mouthAsymmetry))
        });
      }



      // Basic synkinesis detection using real-time landmark data
      const synkinesisDetected = this.detectBasicSynkinesis();
      if (synkinesisDetected.length > 0) {
        asymmetries.push(...synkinesisDetected);
      }
    } else {
      // No fallback to mock data - return empty array if no real data available
      console.warn('⚠️ WARNING: No landmark data available for asymmetry detection');
      return [];
    }

    return asymmetries;
  }

  /**
   * Convert asymmetry percentage to severity level
   * Updated thresholds based on normal baseline data analysis
   */
  private getSeverityFromPercentage(percentage: number): string {
    if (percentage < 10) return 'Normal';        // Increased from 5% - normal faces can have up to 10% asymmetry
    if (percentage < 20) return 'Mild';          // Increased from 15% - allows for natural variation
    if (percentage < 35) return 'Moderate';      // Increased from 30% - more realistic clinical thresholds
    if (percentage < 55) return 'Moderately Severe'; // Increased from 50% - better clinical correlation
    if (percentage < 80) return 'Severe';        // Increased from 75% - reserves severe for truly impaired
    return 'Total Paralysis';
  }

  /**
   * IMPROVED: Advanced synkinesis detection using the new SynkinesisDetectionService
   */
  private detectBasicSynkinesis(): any[] {
    console.log('🔬 Using advanced synkinesis detection service');

    // Get synkinesis summary from the advanced detection service
    const synkinesisSummary = this.synkinesisDetectionService.getSynkinesisSummary();
    const detectedEvents = this.synkinesisDetectionService.getDetectedSynkinesis();

    console.log('🔬 Synkinesis Summary:', synkinesisSummary);

    const synkinesisDetected: any[] = [];

    // Convert advanced detection results to legacy format for compatibility
    if (synkinesisSummary.oralOcularEvents > 0) {
      const oralOcularEvents = detectedEvents.filter(e => e.type === 'oral-ocular');
      const avgSeverity = this.calculateAverageSeverity(oralOcularEvents);
      const avgPercentage = this.calculateAveragePercentage(oralOcularEvents, 'eyeApertureChange');

      synkinesisDetected.push({
        feature: 'Oral-Ocular Synkinesis',
        severity: avgSeverity,
        measurement: `${avgPercentage.toFixed(1)}% unintended eye closure during smile`,
        recommendation: 'Consider synkinesis management therapy'
      });
    }

    if (synkinesisSummary.oculoOralEvents > 0) {
      const oculoOralEvents = detectedEvents.filter(e => e.type === 'oculo-oral');
      const avgSeverity = this.calculateAverageSeverity(oculoOralEvents);
      const avgPercentage = this.calculateAveragePercentage(oculoOralEvents, 'mouthMovementChange');

      synkinesisDetected.push({
        feature: 'Oculo-Oral Synkinesis',
        severity: avgSeverity,
        measurement: `${avgPercentage.toFixed(1)}% unintended mouth movement during eye closure`,
        recommendation: 'Consider botulinum toxin evaluation'
      });
    }

    // If no events detected by advanced service, fall back to legacy detection
    if (synkinesisDetected.length === 0) {
      console.log('🔬 No synkinesis detected by advanced service, using legacy fallback');
      return this.detectLegacySynkinesis();
    }

    console.log(`🔬 Advanced synkinesis detection found ${synkinesisDetected.length} patterns`);
    return synkinesisDetected;
  }

  /**
   * Calculate average severity from synkinesis events
   */
  private calculateAverageSeverity(events: any[]): string {
    if (events.length === 0) return 'None';

    const severityScores = events.map(e => {
      switch (e.severity) {
        case 'mild': return 1;
        case 'moderate': return 2;
        case 'severe': return 3;
        default: return 0;
      }
    });

    const avgScore = severityScores.reduce((sum: number, score: number) => sum + score, 0) / severityScores.length;

    if (avgScore >= 2.5) return 'Severe';
    if (avgScore >= 1.5) return 'Moderate';
    return 'Mild';
  }

  /**
   * Calculate average percentage from synkinesis events
   */
  private calculateAveragePercentage(events: any[], metricType: 'eyeApertureChange' | 'mouthMovementChange'): number {
    if (events.length === 0) return 0;

    const percentages = events.map(e => e.metrics[metricType] * 100);
    return percentages.reduce((sum, pct) => sum + pct, 0) / percentages.length;
  }

  /**
   * Legacy synkinesis detection for fallback compatibility
   */
  private detectLegacySynkinesis(): any[] {
    const synkinesisDetected: any[] = [];

    // Check if we have the necessary landmark data
    const baseline = this.landmarkStorage.get('baseline');
    const eyeClose = this.landmarkStorage.get('eyeClose');
    const smile = this.landmarkStorage.get('smile');

    if (!baseline || !eyeClose || !smile) {
      console.log('Insufficient landmark data for legacy synkinesis detection');
      return synkinesisDetected;
    }

    // Detect eye-mouth synkinesis (unintended mouth movement during eye closure)
    const eyeMouthSynkinesis = this.detectEyeMouthSynkinesis(baseline, eyeClose);
    if (eyeMouthSynkinesis.detected) {
      synkinesisDetected.push({
        feature: 'Eye-Mouth Synkinesis (Legacy)',
        severity: eyeMouthSynkinesis.severity,
        measurement: `${eyeMouthSynkinesis.percentage.toFixed(1)}% unintended mouth movement during eye closure`,
        recommendation: 'Consider synkinesis management therapy'
      });
    }

    // Detect mouth-eye synkinesis (unintended eye movement during smile)
    const mouthEyeSynkinesis = this.detectMouthEyeSynkinesis(baseline, smile);
    if (mouthEyeSynkinesis.detected) {
      synkinesisDetected.push({
        feature: 'Mouth-Eye Synkinesis (Legacy)',
        severity: mouthEyeSynkinesis.severity,
        measurement: `${mouthEyeSynkinesis.percentage.toFixed(1)}% unintended eye movement during smile`,
        recommendation: 'Consider botulinum toxin evaluation'
      });
    }

    return synkinesisDetected;
  }

  /**
   * Detect eye-mouth synkinesis using consistent landmark methodology
   * Uses same mouth analysis landmarks as primary smile analysis for consistency
   */
  private detectEyeMouthSynkinesis(baseline: any[], eyeClose: any[]): any {
    if (baseline.length < 468 || eyeClose.length < 468) {
      return { detected: false, percentage: 0, severity: 'None' };
    }

    try {
      // Use same mouth landmarks as primary mouth analysis for consistency
      const leftCornerBaseline = baseline[61];
      const rightCornerBaseline = baseline[291];
      const upperLipBaseline = baseline[13];

      const leftCornerEyeClose = eyeClose[61];
      const rightCornerEyeClose = eyeClose[291];
      const upperLipEyeClose = eyeClose[13];

      if (!leftCornerBaseline || !rightCornerBaseline || !upperLipBaseline ||
          !leftCornerEyeClose || !rightCornerEyeClose || !upperLipEyeClose) {
        return { detected: false, percentage: 0, severity: 'None' };
      }

      // Calculate face size for normalization (distance between face reference points)
      const faceWidth = Math.sqrt(
        Math.pow(baseline[454].x - baseline[234].x, 2) +
        Math.pow(baseline[454].y - baseline[234].y, 2)
      );

      // Calculate mouth movement during eye closure using same methodology as primary analysis
      const leftCornerMovement = Math.sqrt(
        Math.pow(leftCornerEyeClose.x - leftCornerBaseline.x, 2) +
        Math.pow(leftCornerEyeClose.y - leftCornerBaseline.y, 2)
      );

      const rightCornerMovement = Math.sqrt(
        Math.pow(rightCornerEyeClose.x - rightCornerBaseline.x, 2) +
        Math.pow(rightCornerEyeClose.y - rightCornerBaseline.y, 2)
      );

      const upperLipMovement = Math.sqrt(
        Math.pow(upperLipEyeClose.x - upperLipBaseline.x, 2) +
        Math.pow(upperLipEyeClose.y - upperLipBaseline.y, 2)
      );

      // Normalize movements relative to face size
      const normalizedLeftMovement = (leftCornerMovement / faceWidth) * 100;
      const normalizedRightMovement = (rightCornerMovement / faceWidth) * 100;
      const normalizedUpperLipMovement = (upperLipMovement / faceWidth) * 100;

      // Calculate average normalized movement
      const averageMovement = (normalizedLeftMovement + normalizedRightMovement + normalizedUpperLipMovement) / 3;

      // Clinical thresholds aligned with medical standards - reduced false positives
      const detected = averageMovement > 15; // 15% normalized movement threshold (matches ClinicalAnalysisService)
      const severity = averageMovement > 35 ? 'Severe' : averageMovement > 25 ? 'Moderate' : 'Mild';

      console.log(`Eye-mouth synkinesis: ${averageMovement.toFixed(2)}% normalized movement, detected: ${detected}`);

      return { detected, percentage: averageMovement, severity };
    } catch (error) {
      console.error('Error in eye-mouth synkinesis detection:', error);
      return { detected: false, percentage: 0, severity: 'None' };
    }
  }

  /**
   * Detect mouth-eye synkinesis using consistent landmark methodology
   * Uses same eye closure landmarks as primary eye analysis for consistency
   * IMPROVED: Accounts for natural Duchenne response and reduces blink-related false positives
   */
  private detectMouthEyeSynkinesis(baseline: any[], smile: any[]): any {
    if (baseline.length < 468 || smile.length < 468) {
      return { detected: false, percentage: 0, severity: 'None' };
    }

    try {
      // Use same eye landmarks as primary eye closure analysis for consistency
      const leftEyeLandmarks = [159, 145, 158, 153, 160, 144]; // Left eye closure landmarks
      const rightEyeLandmarks = [386, 374, 385, 380, 387, 373]; // Right eye closure landmarks

      // Calculate left eye closure during smile using same methodology as primary analysis
      const leftEyeBaselinePoints = leftEyeLandmarks.map(idx => baseline[idx]);
      const leftEyeSmilePoints = leftEyeLandmarks.map(idx => smile[idx]);

      if (leftEyeBaselinePoints.some(point => !point) || leftEyeSmilePoints.some(point => !point)) {
        return { detected: false, percentage: 0, severity: 'None' };
      }

      // Calculate vertical distances for left eye
      const leftBaselineDistances = [
        Math.abs(leftEyeBaselinePoints[0].y - leftEyeBaselinePoints[1].y), // 159-145
        Math.abs(leftEyeBaselinePoints[2].y - leftEyeBaselinePoints[3].y), // 158-153
        Math.abs(leftEyeBaselinePoints[4].y - leftEyeBaselinePoints[5].y)  // 160-144
      ];
      const leftSmileDistances = [
        Math.abs(leftEyeSmilePoints[0].y - leftEyeSmilePoints[1].y),
        Math.abs(leftEyeSmilePoints[2].y - leftEyeSmilePoints[3].y),
        Math.abs(leftEyeSmilePoints[4].y - leftEyeSmilePoints[5].y)
      ];

      // Calculate right eye closure during smile
      const rightEyeBaselinePoints = rightEyeLandmarks.map(idx => baseline[idx]);
      const rightEyeSmilePoints = rightEyeLandmarks.map(idx => smile[idx]);

      if (rightEyeBaselinePoints.some(point => !point) || rightEyeSmilePoints.some(point => !point)) {
        return { detected: false, percentage: 0, severity: 'None' };
      }

      // Calculate vertical distances for right eye
      const rightBaselineDistances = [
        Math.abs(rightEyeBaselinePoints[0].y - rightEyeBaselinePoints[1].y), // 386-374
        Math.abs(rightEyeBaselinePoints[2].y - rightEyeBaselinePoints[3].y), // 385-380
        Math.abs(rightEyeBaselinePoints[4].y - rightEyeBaselinePoints[5].y)  // 387-373
      ];
      const rightSmileDistances = [
        Math.abs(rightEyeSmilePoints[0].y - rightEyeSmilePoints[1].y),
        Math.abs(rightEyeSmilePoints[2].y - rightEyeSmilePoints[3].y),
        Math.abs(rightEyeSmilePoints[4].y - rightEyeSmilePoints[5].y)
      ];

      // Calculate average eye closure change during smile
      const leftBaselineMean = leftBaselineDistances.reduce((sum, val) => sum + val, 0) / leftBaselineDistances.length;
      const leftSmileMean = leftSmileDistances.reduce((sum, val) => sum + val, 0) / leftSmileDistances.length;
      const rightBaselineMean = rightBaselineDistances.reduce((sum, val) => sum + val, 0) / rightBaselineDistances.length;
      const rightSmileMean = rightSmileDistances.reduce((sum, val) => sum + val, 0) / rightSmileDistances.length;

      // Calculate closure percentage change during smile (normalized)
      const leftClosureChange = leftBaselineMean > 0 ?
        Math.abs(leftSmileMean - leftBaselineMean) / leftBaselineMean * 100 : 0;
      const rightClosureChange = rightBaselineMean > 0 ?
        Math.abs(rightSmileMean - rightBaselineMean) / rightBaselineMean * 100 : 0;

      // Average the closure changes (already in percentage form)
      const rawAverageChange = (leftClosureChange + rightClosureChange) / 2;

      // IMPROVED: Apply Duchenne response correction for natural eye narrowing during genuine smiles
      // Normal smiles can cause 5-10% eye closure due to cheek elevation
      const duchenneCorrectionFactor = 8.0; // Normal Duchenne response baseline
      const correctedAverageChange = Math.max(0, rawAverageChange - duchenneCorrectionFactor);

      // IMPROVED: More conservative thresholds to reduce false positives from normal facial expressions
      const detected = correctedAverageChange > 25; // 25% change threshold after Duchenne correction
      const severity = correctedAverageChange > 50 ? 'Severe' : correctedAverageChange > 35 ? 'Moderate' : 'Mild';

      console.log(`Mouth-eye synkinesis: Raw=${rawAverageChange.toFixed(2)}%, Corrected=${correctedAverageChange.toFixed(2)}%, detected: ${detected}`);

      return { detected, percentage: correctedAverageChange, severity };
    } catch (error) {
      console.error('Error in mouth-eye synkinesis detection:', error);
      return { detected: false, percentage: 0, severity: 'None' };
    }
  }

  /**
   * Get clinical recommendation based on severity
   */
  private getRecommendationForSeverity(severity: string): string {
    switch (severity.toLowerCase()) {
      case 'normal':
        return 'Continue current management';
      case 'mild':
        return 'Monitor for changes, consider facial exercises';
      case 'moderate':
        return 'Facial rehabilitation therapy recommended';
      case 'moderately severe':
        return 'Intensive facial rehabilitation and neuromuscular retraining';
      case 'severe':
      case 'total paralysis':
        return 'Consider surgical consultation and intensive management';
      default:
        return 'Follow-up assessment recommended';
    }
  }

  private calculateOverallScore(): number {
    // Calculate overall examination score based on clinical analysis
    if (this.clinicalResults) {
      const asymmetryIndex = this.clinicalResults.composite_scores.facial_asymmetry_index;
      // Convert asymmetry index to percentage score (lower asymmetry = higher score)
      return Math.max(0, 100 - (asymmetryIndex * 100));
    }
    return 87.5; // Fallback
  }

  /**
   * Store peak frame landmarks for a specific action type
   * Used by the peak tracking system to store the best frame
   */
  private storePeakFrameLandmarks(landmarks: any[], actionType: string): void {
    console.log(`🎯 Storing peak frame landmarks for ${actionType} with ${landmarks.length} landmarks`);

    // Validate landmark data before storage
    if (!landmarks || !Array.isArray(landmarks)) {
      console.error('Invalid peak frame landmark data: not an array', landmarks);
      return;
    }

    if (landmarks.length < 400) {
      console.error('Invalid peak frame landmark count:', landmarks.length, 'expected at least 400');
      return;
    }

    // Validate landmark structure
    const invalidLandmarks = landmarks.filter((landmark, index) => {
      if (!landmark || typeof landmark !== 'object') {
        console.warn(`Peak frame landmark ${index} is not an object:`, landmark);
        return true;
      }
      if (typeof landmark.x !== 'number' || typeof landmark.y !== 'number') {
        console.warn(`Peak frame landmark ${index} missing x/y coordinates:`, landmark);
        return true;
      }
      return false;
    });

    if (invalidLandmarks.length > 0) {
      console.error(`Found ${invalidLandmarks.length} invalid peak frame landmarks out of ${landmarks.length}`);
      return;
    }

    // Map action types to storage keys
    const actionMap = [
      { keys: ['baseline', 'neutral'], storage: 'baseline' },
      { keys: ['eyebrow', 'raise'], storage: 'eyebrowRaise' },
      { keys: ['eye', 'close'], storage: 'eyeClose' },
      { keys: ['smile'], storage: 'smile' },
      { keys: ['pucker', 'lips'], storage: 'lipPucker' },
      { keys: ['cheek', 'puff'], storage: 'cheekPuff' }
    ];

    // Find matching storage key
    let storageKey = '';
    const normalizedActionType = actionType.toLowerCase();

    for (const map of actionMap) {
      if (map.keys.some(k => normalizedActionType.includes(k))) {
        storageKey = map.storage;
        break;
      }
    }

    if (!storageKey) {
      console.warn(`🎯 Unknown action type for peak frame storage: ${actionType}`);
      return;
    }

    // Create a deep copy of landmarks to ensure data integrity
    const landmarksCopy = landmarks.map(landmark => ({
      x: landmark.x,
      y: landmark.y,
      z: landmark.z || 0
    }));

    // Store the peak frame landmarks
    this.landmarkStorage.set(storageKey, landmarksCopy);
    if (storageKey === 'baseline') this.baselineLandmarks = [...landmarksCopy];

    console.log(`🎯 ✓ Stored PEAK FRAME for ${storageKey}: ${landmarksCopy.length} landmarks`);

    // Log storage state
    const storageState = Array.from(this.landmarkStorage.keys()).map(key => ({
      action: key,
      count: this.landmarkStorage.get(key)?.length || 0
    }));
    console.log('🎯 Updated landmark storage state:', storageState);
  }

  /**
   * Store landmarks for clinical analysis based on current action
   */
  private storeLandmarksForAnalysis(landmarks: any[]): void {
    const currentAction = this.examOrchestrator.getCurrentAction();
    if (!currentAction) {
      console.log('No current action available for landmark storage');
      return;
    }

    // Validate landmark data before storage
    if (!landmarks || !Array.isArray(landmarks)) {
      console.error('Invalid landmark data: not an array', landmarks);
      return;
    }

    // MediaPipe Face Mesh can return 468 (v1) or 478 (v2 with iris) landmarks
    const expectedLandmarkCounts = [468, 478];
    if (!expectedLandmarkCounts.includes(landmarks.length)) {
      console.warn(`Landmark validation failed: Expected 468 or 478 landmarks, got ${landmarks.length}`);
      // Don't return here - log the issue but still store what we have for debugging
    } else {
      console.log(`✓ MediaPipe landmark count is valid: ${landmarks.length} landmarks`);
    }

    // Validate landmark structure - each landmark should have x, y, z coordinates
    const invalidLandmarks = landmarks.filter((landmark, index) => {
      if (!landmark || typeof landmark !== 'object') {
        console.warn(`Landmark ${index} is not an object:`, landmark);
        return true;
      }
      if (typeof landmark.x !== 'number' || typeof landmark.y !== 'number') {
        console.warn(`Landmark ${index} missing x/y coordinates:`, landmark);
        return true;
      }
      return false;
    });

    if (invalidLandmarks.length > 0) {
      console.error(`Found ${invalidLandmarks.length} invalid landmarks out of ${landmarks.length}`);
      // Don't store invalid data
      return;
    }

    // Update current action name
    this.currentActionName = currentAction.name.toLowerCase();
    console.log('=== LANDMARK STORAGE DEBUG ===');
    console.log('Raw action name:', currentAction.name);
    console.log('Lowercase action name:', this.currentActionName);
    console.log('Storing landmarks for action:', this.currentActionName, 'with', landmarks.length, 'valid landmarks');

    // Store landmarks based on action type
    // Handle both the actual action names from config and normalized versions
    const normalizedActionName = this.currentActionName.toLowerCase().replace(/[^a-z]/g, '');
    console.log('Normalized action name:', normalizedActionName);

    // Map of all possible action name variants to storage keys
    // Updated to match the actual action names from ExamActionsConfig
    // Note: Removed lipPucker and cheekPuff as per user preference
    const actionMap = [
      { keys: ['neutral', 'neutral_face', 'neutral / resting face', 'neutralrestingface', 'resting', 'restingface', 'neutral/restingface'], storage: 'baseline' },
      { keys: ['eyebrow_raise', 'eyebrow_elevation', 'raise eyebrows', 'raiseeyebrows', 'eyebrowraise', 'raise', 'eyebrow'], storage: 'eyebrowRaise' },
      { keys: ['eye_close', 'gentle_eye_closure', 'close eyes tightly', 'closeeyestightly', 'eyeclose', 'eye', 'close', 'closeeyes'], storage: 'eyeClose' },
      { keys: ['smile', 'show_teeth', 'smile wide', 'smilewide', 'smiling'], storage: 'smile' }
    ];

    let recognized = false;
    for (const map of actionMap) {
      if (map.keys.some(k => this.currentActionName.includes(k) || normalizedActionName.includes(k))) {
        // Create a deep copy of landmarks to ensure data integrity
        const landmarksCopy = landmarks.map(landmark => ({
          x: landmark.x,
          y: landmark.y,
          z: landmark.z || 0 // Ensure z coordinate exists
        }));

        this.landmarkStorage.set(map.storage, landmarksCopy);
        if (map.storage === 'baseline') this.baselineLandmarks = [...landmarksCopy];

        console.log(`✓ Stored ${map.storage} landmarks: ${landmarksCopy.length} points`);

        // Validate stored data - accept both 468 and 478 landmarks
        if (expectedLandmarkCounts.includes(landmarksCopy.length)) {
          console.log(`✓ ${map.storage} landmark count is correct (${landmarksCopy.length} points)`);
        } else {
          console.warn(`⚠ ${map.storage} landmark count is incorrect: ${landmarksCopy.length}/${expectedLandmarkCounts.join(' or ')}`);
        }

        recognized = true;
        break;
      }
    }

    if (!recognized) {
      console.warn('Landmark storage: Action not recognized:', this.currentActionName, 'Landmarks:', landmarks.length);
      console.warn('Available action keys:', actionMap.map(m => m.keys.join(', ')).join(' | '));
    }

    // Log current state of landmarkStorage with validation
    // Note: Removed lipPucker and cheekPuff as per user preference
    const keys = ['baseline','eyebrowRaise','eyeClose','smile'];
    const storageState = keys.map(k => {
      const stored = this.landmarkStorage.get(k) || [];
      return {
        action: k,
        count: stored.length,
        valid: expectedLandmarkCounts.includes(stored.length) ? '✓' : '⚠'
      };
    });
    console.log('Landmark storage state:', storageState);

    // Check if we have all required landmarks for clinical analysis
    const requiredActions = ['baseline', 'eyebrowRaise', 'eyeClose', 'smile'];
    const missingActions = requiredActions.filter(action => {
      const stored = this.landmarkStorage.get(action);
      return !stored || !expectedLandmarkCounts.includes(stored.length);
    });

    if (missingActions.length === 0) {
      console.log('✓ All required landmark data collected for clinical analysis');
    } else {
      console.log(`⚠ Missing or incomplete landmark data for: ${missingActions.join(', ')}`);
    }
  }

  /**
   * Calculate individual eyebrow elevation in degrees
   */
  private calculateEyebrowElevation(side: 'left' | 'right'): number {
    const baseline = this.landmarkStorage.get('baseline');
    const eyebrowRaise = this.landmarkStorage.get('eyebrowRaise');

    console.log(`calculateEyebrowElevation(${side}):`, {
      hasBaseline: !!baseline,
      hasEyebrowRaise: !!eyebrowRaise,
      baselineLength: baseline?.length || 0,
      eyebrowRaiseLength: eyebrowRaise?.length || 0
    });

    if (!baseline || !eyebrowRaise) {
      console.log(`Missing landmark data for eyebrow elevation (${side})`);
      return 0;
    }

    const landmarkIndex = side === 'left' ? 105 : 334; // MediaPipe eyebrow landmarks
    const baselinePoint = baseline[landmarkIndex];
    const raisedPoint = eyebrowRaise[landmarkIndex];

    console.log(`Eyebrow landmarks (${side}):`, {
      landmarkIndex,
      baselinePoint,
      raisedPoint
    });

    if (!baselinePoint || !raisedPoint) {
      console.log(`Missing specific landmark points for eyebrow (${side})`);
      return 0;
    }

    // Calculate vertical displacement and convert to degrees
    const verticalDisplacement = Math.abs(baselinePoint.y - raisedPoint.y);
    // Convert normalized coordinates to approximate degrees (rough estimation)
    const degrees = verticalDisplacement * 180; // Scale factor for degrees

    console.log(`Eyebrow calculation (${side}):`, {
      verticalDisplacement,
      degrees
    });

    return Math.round(degrees * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Calculate individual eye closure percentage using Eye Aspect Ratio
   */
  private calculateEyeClosure(side: 'left' | 'right'): number {
    const baseline = this.landmarkStorage.get('baseline');
    const eyeClose = this.landmarkStorage.get('eyeClose');

    if (!baseline || !eyeClose) return 0;

    // MediaPipe eye landmarks
    const verticalIndices = side === 'left' ? [159, 145] : [386, 374];
    const horizontalIndices = side === 'left' ? [33, 133] : [362, 263];

    // Calculate baseline EAR
    const baselineEAR = this.calculateEAR(baseline, verticalIndices, horizontalIndices);
    const closedEAR = this.calculateEAR(eyeClose, verticalIndices, horizontalIndices);

    if (baselineEAR === 0) return 0;

    // Calculate closure percentage
    const closurePercentage = Math.max(0, ((baselineEAR - closedEAR) / baselineEAR) * 100);
    return Math.round(closurePercentage * 100) / 100;
  }

  /**
   * Calculate Eye Aspect Ratio (EAR)
   */
  private calculateEAR(landmarks: any[], verticalIndices: number[], horizontalIndices: number[]): number {
    const p1 = landmarks[verticalIndices[0]];
    const p2 = landmarks[verticalIndices[1]];
    const p3 = landmarks[horizontalIndices[0]];
    const p4 = landmarks[horizontalIndices[1]];

    if (!p1 || !p2 || !p3 || !p4) return 0;

    const verticalDist = Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));
    const horizontalDist = Math.sqrt(Math.pow(p3.x - p4.x, 2) + Math.pow(p3.y - p4.y, 2));

    return horizontalDist > 0 ? verticalDist / horizontalDist : 0;
  }

  /**
   * Calculate individual mouth corner movement using expert's 3D pose-invariant approach
   */
  private calculateMouthMovement(side: 'left' | 'right'): number {
    const baseline = this.landmarkStorage.get('baseline');
    const smile = this.landmarkStorage.get('smile');

    if (!baseline || !smile) return 0;

    // Use the expert's 3D pose-invariant analysis
    const result = this.analyze_smile_movement(baseline, smile);

    // Return the appropriate side's normalized movement
    // Expert's approach returns normalized values (0-1 scale relative to face width)
    // Convert to reasonable millimeter-like display values
    const movement = side === 'left' ? result.left_movement : result.right_movement;

    // Expert's values are typically 0.01-0.3 (normalized by face width)
    // Convert to realistic mm display: multiply by 20-30 for reasonable values
    const millimeters = movement * 25; // More realistic scaling for display

    console.log(`🔬 Expert's 3D Mouth Movement (${side}): ${movement.toFixed(6)} normalized → ${millimeters.toFixed(2)}mm display`);
    console.log(`🔬 Raw expert result:`, result);

    return Math.round(millimeters * 100) / 100;
  }



  /**
   * Calculate real-time symmetry metrics when clinical analysis is not available
   */
  private calculateRealTimeSymmetryMetrics(): any {
    console.log('=== CALCULATING REAL-TIME SYMMETRY METRICS ===');
    console.log('Landmark storage size:', this.landmarkStorage.size);
    console.log('Available landmark keys:', Array.from(this.landmarkStorage.keys()));

    // Debug landmark storage contents
    for (const [key, landmarks] of this.landmarkStorage.entries()) {
      console.log(`${key} landmarks count:`, landmarks?.length || 0);
      if (landmarks && landmarks.length > 0) {
        console.log(`${key} sample landmark:`, landmarks[0]);
      }
    }

    const leftEyebrow = this.calculateEyebrowElevation('left');
    const rightEyebrow = this.calculateEyebrowElevation('right');
    const eyebrowAsymmetry = this.calculateAsymmetryPercentage(leftEyebrow, rightEyebrow);
    console.log('Eyebrow calculations:', { leftEyebrow, rightEyebrow, eyebrowAsymmetry });

    const leftEye = this.calculateEyeClosure('left');
    const rightEye = this.calculateEyeClosure('right');
    const eyeAsymmetry = this.calculateAsymmetryPercentage(leftEye, rightEye);
    console.log('Eye calculations:', { leftEye, rightEye, eyeAsymmetry });

    const leftMouth = this.calculateMouthMovement('left');
    const rightMouth = this.calculateMouthMovement('right');

    // Use expert's asymmetry calculation directly
    const baseline = this.landmarkStorage.get('baseline');
    const smile = this.landmarkStorage.get('smile');
    let mouthAsymmetry = 0;

    if (baseline && smile) {
      const result = this.analyze_smile_movement(baseline, smile);
      mouthAsymmetry = result.asymmetry_index * 100; // Convert to percentage
      console.log(`🔬 Expert's Asymmetry: ${result.asymmetry_index.toFixed(6)} → ${mouthAsymmetry.toFixed(1)}%`);
    } else {
      mouthAsymmetry = this.calculateAsymmetryPercentage(leftMouth, rightMouth);
    }

    console.log('Mouth calculations (Expert 3D):', { leftMouth, rightMouth, mouthAsymmetry });

    // Calculate House-Brackmann grade for real-time analysis
    const housebrackmannGrade = this.calculateRealTimeHouseBrackmannGrade(
      eyebrowAsymmetry,
      eyeAsymmetry,
      mouthAsymmetry
    );

    console.log('Real-time symmetry metrics - House-Brackmann grade:', housebrackmannGrade);

    return {
      // Individual measurements
      leftEyebrowElevation: leftEyebrow,
      rightEyebrowElevation: rightEyebrow,
      eyebrowAsymmetry: eyebrowAsymmetry,

      leftEyeClosure: leftEye,
      rightEyeClosure: rightEye,
      eyeAsymmetry: eyeAsymmetry,

      leftMouthMovement: leftMouth,
      rightMouthMovement: rightMouth,
      mouthAsymmetry: mouthAsymmetry,

      // Overall scores
      eyebrowSymmetry: (() => {
        const score = Math.max(0, 100 - eyebrowAsymmetry);
        console.log(`🏥 EYEBROW OVERALL SCORE: ${eyebrowAsymmetry.toFixed(1)}% asymmetry → ${score.toFixed(1)}% symmetry`);
        return score;
      })(),
      eyeSymmetry: (() => {
        const score = Math.max(0, 100 - eyeAsymmetry);
        console.log(`🏥 EYE OVERALL SCORE: ${eyeAsymmetry.toFixed(1)}% asymmetry → ${score.toFixed(1)}% symmetry`);
        return score;
      })(),
      mouthSymmetry: (() => {
        const score = Math.max(0, 100 - mouthAsymmetry);
        console.log(`🏥 MOUTH OVERALL SCORE: ${mouthAsymmetry.toFixed(1)}% asymmetry → ${score.toFixed(1)}% symmetry`);
        return score;
      })(),

      // House-Brackmann grade for real-time analysis
      housebrackmannGrade: housebrackmannGrade,

      // FIXED: Standardized weighting system consistent across application
      // Eyebrow (30%) + Eye (40%) + Mouth (30%) - balanced clinical importance
      overallSymmetry: Math.max(0, 100 - (
        (eyebrowAsymmetry * 0.30) +     // 30% weight for eyebrow
        (eyeAsymmetry * 0.40) +         // 40% weight for eye (most critical)
        (mouthAsymmetry * 0.30)         // 30% weight for mouth (standardized)
      ))
    };
  }

  /**
   * Calculate asymmetry percentage between two values
   * FIXED: Use average instead of max to avoid division by very small numbers
   */
  private calculateAsymmetryPercentage(leftValue: number, rightValue: number): number {
    console.log(`🔧 ASYMMETRY CALCULATION DEBUG:`);
    console.log(`   Input: Left=${leftValue.toFixed(2)}, Right=${rightValue.toFixed(2)}`);

    if (leftValue === 0 && rightValue === 0) {
      console.log(`   Result: 0% (both values are zero)`);
      return 0;
    }

    // Use average instead of max to avoid division by very small numbers
    const avgValue = (leftValue + rightValue) / 2;
    if (avgValue === 0) {
      console.log(`   Result: 0% (average is zero)`);
      return 0;
    }

    const asymmetryPercentage = Math.abs(leftValue - rightValue) / avgValue * 100;
    const cappedAsymmetry = Math.min(asymmetryPercentage, 100);

    console.log(`   Average: ${avgValue.toFixed(2)}`);
    console.log(`   Difference: ${Math.abs(leftValue - rightValue).toFixed(2)}`);
    console.log(`   Raw asymmetry: ${asymmetryPercentage.toFixed(2)}%`);
    console.log(`   Capped asymmetry: ${cappedAsymmetry.toFixed(2)}%`);

    // Cap asymmetry at reasonable maximum to prevent extreme values
    return cappedAsymmetry;
  }

  // updateActionVisualization method removed - Action View no longer exists

  private getCurrentActionType(): ExamAction {
    // Get current action from exam orchestrator
    const currentAction = this.examOrchestrator.getCurrentAction();
    if (!currentAction) return 'neutral';

    // Extract action name from the action object
    const actionName = typeof currentAction === 'string' ? currentAction :
                      currentAction.instruction || currentAction.name || '';

    console.log('Current action name:', actionName); // Debug log

    // Map action names to ExamAction types with more flexible matching
    const lowerActionName = actionName.toLowerCase();

    if (lowerActionName.includes('eyebrow') || lowerActionName.includes('raise')) {
      return 'eyebrow_raise';
    } else if (lowerActionName.includes('eye') && lowerActionName.includes('close')) {
      return 'eye_close';
    } else if (lowerActionName.includes('smile')) {
      return 'smile';
    } else if (lowerActionName.includes('neutral')) {
      return 'neutral';
    } else {
      // Default mapping based on common patterns
      switch (lowerActionName) {
        case 'neutral face':
        case 'rest':
        case 'relax':
          return 'neutral';
        case 'eyebrow elevation':
        case 'forehead wrinkle':
          return 'eyebrow_raise';
        case 'gentle eye closure':
        case 'eye squeeze':
          return 'eye_close';
        case 'show teeth':
        case 'big smile':
          return 'smile';
        default:
          console.log('Unknown action, defaulting to neutral:', actionName);
          return 'neutral';
      }
    }
  }

  private calculateActionProgress(): number {
    // Mock progress calculation - in real implementation this would be based on:
    // - Time spent on action
    // - Quality of facial expression detection
    // - Completion criteria
    const currentSession = this.examOrchestrator.getCurrentSession();
    if (!currentSession) return 0;

    // Simple time-based progress for demo
    const actionStartTime = Date.now() - 3000; // Mock start time
    const elapsedTime = Date.now() - actionStartTime;
    const targetTime = 5000; // 5 seconds per action

    return Math.min(100, (elapsedTime / targetTime) * 100);
  }

  private updateUI(): void {
    const currentAction = this.examOrchestrator.getCurrentAction();
    if (currentAction) {
      this.displayInstruction(currentAction.instruction);
    }

    // Note: Action visualization update removed - Action View no longer exists

    // Update button states based on current action
    this.updateButtonStates();
  }

  private displayInstruction(instruction: string): void {
    // Prevent further instructions after exam is completed
    if (this.examCompleted) return;
    // Only log when instruction actually changes
    // Optionally, track last spoken text via SpeechService if needed
    // if (this.speechService.getLastSpokenText() !== instruction) {
    //   console.log('displayInstruction called with new instruction:', instruction);
    // }

    // Get current action for enhanced instruction display
    const currentAction = this.examOrchestrator.getCurrentAction();
    let displayText: string;
    let speechText: string;
    if (currentAction && currentAction.name) {
      // Only log when action name changes
      if (this._lastActionName !== currentAction.name) {
        console.log('Current action name:', currentAction.name);
        this._lastActionName = currentAction.name;
      }
      // Enhanced instruction with action name and details
      const actionName = currentAction.name.replace(/_/g, ' ').toUpperCase();
      displayText = `${actionName}: ${instruction}`;
      // For speech, use shorter, more direct format
      speechText = this.getShortSpeechInstruction(currentAction.name, instruction);
    } else {
      // Fallback to basic instruction
      displayText = instruction;
      speechText = instruction;
    }
    // Update visual display using InstructionView
    this.instructionView.setInstruction(displayText);
    // Only speak if the instruction is different from the last spoken
    if (this.speechService.getLastSpokenText() !== speechText) {
      this.speechService.speakInstruction(speechText);
    }
  }

  /**
   * Show examination interface and hide patient form
   */
  private showExamInterface(): void {
    console.log('Showing examination interface...');

    // Hide patient form
    const patientForm = document.getElementById('patientForm');
    if (patientForm) {
      patientForm.style.display = 'none';
      console.log('Patient form hidden');
    }

    // Show examination output (camera views)
    const outputDiv = document.getElementById('output');
    if (outputDiv) {
      outputDiv.style.display = 'block';
      console.log('Output div shown');
    }

    // Show instructions
    const instructions = document.querySelector('.instructions');
    if (instructions) {
      (instructions as HTMLElement).style.display = 'block';
      console.log('Instructions shown');
    }

    // Show instruction area below camera views
    const instructionArea = document.getElementById('instructionArea');
    if (instructionArea) {
      instructionArea.style.display = 'block';
      console.log('Instruction area shown');
    }

    // Show instruction element
    const instructionElement = document.getElementById('instruction');
    if (instructionElement) {
      instructionElement.style.display = 'block';
      console.log('Instruction element shown');
    }

    // Show next button
    const nextBtn = document.getElementById('nextBtn');
    if (nextBtn) {
      nextBtn.style.display = 'inline-block';
      console.log('Next button shown');
    }

    // Show speech toggle button
    const speechToggleBtn = document.getElementById('speechToggleBtn');
    if (speechToggleBtn) {
      speechToggleBtn.style.display = 'inline-block';
      this.updateSpeechToggleButton(); // Ensure correct initial state
      console.log('Speech toggle button shown');
    }

    // Show test speech button
    const testSpeechBtn = document.getElementById('testSpeechBtn');
    if (testSpeechBtn) {
      testSpeechBtn.style.display = 'inline-block';
      console.log('Test speech button shown');
    }

    console.log('Examination interface setup complete');
  }


  /**
   * Show error message to user
   */
  private showErrorMessage(message: string): void {
    ErrorView.showErrorMessage(message);
  }

  /**
   * Map action name to ExamActionType
   */
  private mapActionToType(actionName: string): ExamActionType {
    const lowerName = actionName.toLowerCase();

    if (lowerName.includes('neutral') || lowerName.includes('resting')) {
      return 'baseline';
    } else if (lowerName.includes('eyebrow') || lowerName.includes('raise')) {
      return 'eyebrowRaise';
    } else if (lowerName.includes('eye') && lowerName.includes('close')) {
      return 'eyeClose';
    } else if (lowerName.includes('smile') || lowerName.includes('teeth')) {
      return 'smile';
    }

    // Default fallback
    return 'baseline';
  }

  /**
   * Update button states based on movement detection
   */
  private updateButtonStatesWithDetection(): void {
    const currentAction = this.examOrchestrator.getCurrentAction();
    if (!currentAction) return;

    const actionType = this.mapActionToType(currentAction.name);
    const detectionStatus = this.movementDetectionService.getDetectionStatus();
    const isCurrentDetected = detectionStatus.get(actionType) || false;

    const nextBtn = document.getElementById('nextBtn') as HTMLButtonElement;
    const finishBtn = document.getElementById('finishBtn') as HTMLButtonElement;

    // Update next button state
    if (nextBtn) {
      if (isCurrentDetected) {
        nextBtn.disabled = false;
        nextBtn.style.opacity = '1';
        nextBtn.style.cursor = 'pointer';
        nextBtn.title = 'Movement detected - click to proceed';
      } else {
        nextBtn.disabled = true;
        nextBtn.style.opacity = '0.5';
        nextBtn.style.cursor = 'not-allowed';
        nextBtn.title = 'Complete the current movement to proceed';
      }
    }

    // Update finish button state (only for final action)
    if (finishBtn && (actionType === 'smile' || this.examOrchestrator.isExamCompleted())) {
      const allDetected = this.movementDetectionService.areAllMovementsDetected();

      if (allDetected) {
        finishBtn.disabled = false;
        finishBtn.style.opacity = '1';
        finishBtn.style.cursor = 'pointer';
        finishBtn.title = 'All movements detected - click to finish examination';
        this.detectionStatusView.showCompletionStatus(true);
      } else {
        finishBtn.disabled = true;
        finishBtn.style.opacity = '0.5';
        finishBtn.style.cursor = 'not-allowed';
        finishBtn.title = 'Complete all movements to finish examination';
        this.detectionStatusView.showCompletionStatus(false);
      }
    }

    // Update regular button states as well
    this.updateButtonStates();
  }

  private generateCSVContent(results: any): string {
    const patient = results.patientInfo;
    const metrics = results.symmetryMetrics;

    let csv = 'Field,Value\n';
    csv += `Patient ID,${patient.id || 'N/A'}\n`;
    csv += `Patient Name,${patient.name || 'N/A'}\n`;
    csv += `Age,${patient.age || 'N/A'}\n`;
    csv += `Exam Date,${new Date(results.timestamp).toLocaleDateString()}\n`;
    csv += `Exam Time,${new Date(results.timestamp).toLocaleTimeString()}\n`;
    csv += `Overall Score,${results.overallScore}%\n`;
    csv += `Eyebrow Symmetry,${metrics.eyebrowSymmetry}%\n`;
    csv += `Eye Symmetry,${metrics.eyeSymmetry}%\n`;
    csv += `Mouth Symmetry,${metrics.mouthSymmetry}%\n`;

    // Add asymmetries
    csv += '\nAsymmetries\n';
    csv += 'Feature,Severity,Measurement,Recommendation\n';
    results.asymmetries.forEach((asymmetry: any) => {
      csv += `${asymmetry.feature},${asymmetry.severity},${asymmetry.measurement},${asymmetry.recommendation}\n`;
    });

    return csv;
  }

  private generateMarkdownContent(results: any): string {
    const patient = results.patientInfo;
    const metrics = results.symmetryMetrics;

    let markdown = '# Facial Symmetry Analysis Results\n\n';
    markdown += '## Patient Information\n\n';
    markdown += `- **Patient ID:** ${patient.id || 'N/A'}\n`;
    markdown += `- **Name:** ${patient.name || 'N/A'}\n`;
    markdown += `- **Age:** ${patient.age || 'N/A'} years\n`;
    markdown += `- **Exam Date:** ${new Date(results.timestamp).toLocaleDateString()}\n`;
    markdown += `- **Exam Time:** ${new Date(results.timestamp).toLocaleTimeString()}\n`;
    markdown += `- **Overall Score:** ${results.overallScore}%\n\n`;

    markdown += '## Symmetry Measurements\n\n';
    markdown += `| Feature | Score | Status |\n`;
    markdown += `|---------|-------|--------|\n`;
    markdown += `| Eyebrow Symmetry | ${metrics.eyebrowSymmetry}% | ${metrics.eyebrowSymmetry >= 85 ? 'Excellent' : metrics.eyebrowSymmetry >= 70 ? 'Good' : 'Needs Attention'} |\n`;
    markdown += `| Eye Symmetry | ${metrics.eyeSymmetry}% | ${metrics.eyeSymmetry >= 85 ? 'Excellent' : metrics.eyeSymmetry >= 70 ? 'Good' : 'Needs Attention'} |\n`;
    markdown += `| Mouth Symmetry | ${metrics.mouthSymmetry}% | ${metrics.mouthSymmetry >= 85 ? 'Excellent' : metrics.mouthSymmetry >= 70 ? 'Good' : 'Needs Attention'} |\n\n`;

    if (results.asymmetries.length > 0) {
      markdown += '## Detected Asymmetries\n\n';
      markdown += `| Feature | Severity | Measurement | Recommendation |\n`;
      markdown += `|---------|----------|-------------|----------------|\n`;
      results.asymmetries.forEach((asymmetry: any) => {
        markdown += `| ${asymmetry.feature} | ${asymmetry.severity} | ${asymmetry.measurement} | ${asymmetry.recommendation} |\n`;
      });
    } else {
      markdown += '## Asymmetries\n\n';
      markdown += '✓ No significant asymmetries detected. Facial symmetry appears to be within normal ranges.\n';
    }

    return markdown;
  }

  private downloadFile(content: string, filename: string, mimeType: string): void {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }



  private highlightRelevantLandmarks(): void {
    const currentAction = this.examOrchestrator.getCurrentAction();
    if (!currentAction || !currentAction.targetMetric) return;

    // Get landmarks for the current metric being measured
    const relevantLandmarks = this.getRelevantLandmarksForMetric(currentAction.targetMetric);

    // Highlight the relevant landmarks in red
    this.visualizationService?.highlightLandmarks(relevantLandmarks, '#ffff00');
  }

  private getRelevantLandmarksForMetric(metric: string): number[] {
    const landmarkMap: Record<string, number[]> = {
      'forehead': [21, 251, 9, 10], // Forehead landmarks
      'eye_gap': [159,145386,374], // Eye landmarks
      'smile': [61, 291, 13, 14], // Mouth corner landmarks
      'lip': [84, 314, 17, 18], // Lip landmarks
      'nose': [1, 2, 5, 4, 6, 19, 20] // Nose landmarks
    };

    return landmarkMap[metric] || [];
  }



  private showResults(): void {
    // Hide exam interface and show results
    const examInterface = document.getElementById('examInterface');
    const resultsInterface = document.getElementById('resultsInterface');

    if (examInterface) examInterface.style.display = 'none';
    if (resultsInterface) resultsInterface.style.display = 'block';
  }

  async exportResults(): Promise<void> {
    try {
      // Use ResultsView for PDF export
      this.resultsView.exportToPDF();
    } catch (error) {
      console.error('Failed to export PDF results:', error);
      alert('Failed to export PDF results');
    }
  }

  /**
   * Store results for the dedicated results route
   */
  private storeResultsForRoute(): void {
    if (!this.examResults) {
      console.error('No exam results to store');
      return;
    }

    try {
      const storageData = {
        results: this.examResults,
        timestamp: new Date().toISOString(),
        expiryTime: new Date(Date.now() + (24 * 60 * 60 * 1000)).toISOString() // 24 hours
      };

      localStorage.setItem('facialSymmetryResults', JSON.stringify(storageData));
      console.log('Results stored successfully for results route');
    } catch (error) {
      console.error('Failed to store results for results route:', error);
    }
  }

  /**
   * Navigate to the dedicated results route
   */
  private navigateToResults(): void {
    console.log('Navigating to results route...');
    window.history.pushState({}, '', '/results');
    window.dispatchEvent(new Event('popstate'));
  }

  // Camera switching functionality
  private async initializeCameraSwitching(): Promise<void> {
    try {
      console.log('Initializing camera switching functionality...');

      // Check if we're on mobile device
      const isMobile = /Mobi|Android/i.test(navigator.userAgent);

      if (!isMobile) {
        console.log('Desktop device detected - camera switching not needed');
        return;
      }

      // Enumerate available cameras
      await this.cameraRepository.enumerateCameras();

      // Show switch camera button if multiple cameras are available
      const switchCameraBtn = document.getElementById('switchCameraBtn');
      if (switchCameraBtn && this.cameraRepository.hasMultipleCameras()) {
        switchCameraBtn.style.display = 'inline-block';
        console.log('Camera switch button shown - multiple cameras detected');

        // Update button text based on current camera
        this.updateCameraSwitchButton();
      } else {
        console.log('Camera switch button hidden - single camera or not found');
      }
    } catch (error) {
      console.error('Error initializing camera switching:', error);
    }
  }

  private async switchCamera(): Promise<void> {
    try {
      console.log('Switching camera...');

      // Show loading state
      const switchCameraBtn = document.getElementById('switchCameraBtn') as HTMLButtonElement;
      if (switchCameraBtn) {
        switchCameraBtn.textContent = '🔄 Switching...';
        switchCameraBtn.disabled = true;
      }

      // Switch camera using repository
      await this.cameraRepository.switchCamera();

      // Update button text
      this.updateCameraSwitchButton();

      // Re-enable button
      if (switchCameraBtn) {
        switchCameraBtn.disabled = false;
      }

      console.log('Camera switched successfully');
    } catch (error) {
      console.error('Error switching camera:', error);

      // Reset button state on error
      const switchCameraBtn = document.getElementById('switchCameraBtn') as HTMLButtonElement;
      if (switchCameraBtn) {
        switchCameraBtn.disabled = false;
        this.updateCameraSwitchButton();
      }

      // Show error message
      this.showErrorMessage('Failed to switch camera. Please try again.');
    }
  }

  private updateCameraSwitchButton(): void {
    const switchCameraBtn = document.getElementById('switchCameraBtn');
    if (switchCameraBtn) {
      const currentFacingMode = this.cameraRepository.getCurrentFacingMode();
      const nextCamera = currentFacingMode === 'user' ? 'Back' : 'Front';
      switchCameraBtn.textContent = `📷 ${nextCamera} Camera`;
      switchCameraBtn.title = `Switch to ${nextCamera.toLowerCase()} camera`;
    }
  }
}
/*  */