// Application Use Case: Process Facial Data
import { ExamSession } from '../../domain/entities/ExamSession.js';
import { MetricResult } from '../../domain/value-objects/MetricResult.js';
import { MetricCalculationService } from '../../domain/services/MetricCalculationService.js';
import { Landmark } from '../../shared/types/index.js';
import { IExamRepository } from '../../domain/repositories/IExamRepository.js';

export interface ProcessFacialDataRequest {
  session: ExamSession;
  landmarks: Landmark[];
}

export class ProcessFacialDataUseCase {
  constructor(
    private metricCalculationService: MetricCalculationService,
    private examRepository: IExamRepository
  ) {}

  async execute(request: ProcessFacialDataRequest): Promise<MetricResult[]> {
    const { session, landmarks } = request;
    
    if (!session.isStarted) {
      return [];
    }

    const currentAction = session.getCurrentAction();
    if (!currentAction) {
      return [];
    }

    // Calculate facial metrics
    const metrics = this.metricCalculationService.calculateMetrics(landmarks);
    
    // Convert to results
    const results: MetricResult[] = [];
    
    if (currentAction.targetMetric) {
      // Single metric for specific action
      const value = metrics.getMetricByName(currentAction.targetMetric);
      const { score, label } = this.metricCalculationService.convertToScore(value);
      
      const result = MetricResult.create({
        actionName: currentAction.name,
        metricName: currentAction.targetMetric,
        value,
        score,
        label
      });
      
      results.push(result);
      session.addResult(result);
    } else {
      // All metrics for neutral face
      const metricsObj = metrics.toObject();
      
      for (const [metricName, value] of Object.entries(metricsObj)) {
        const { score, label } = this.metricCalculationService.convertToScore(value);
        
        const result = MetricResult.create({
          actionName: currentAction.name,
          metricName,
          value,
          score,
          label
        });
        
        results.push(result);
        session.addResult(result);
      }
    }

    // Save results
    await this.examRepository.saveResults(results);
    
    return results;
  }
}
